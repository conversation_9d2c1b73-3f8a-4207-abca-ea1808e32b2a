
import React, { useState } from 'react';
import { Modal, Pressable, Text, TouchableOpacity, View, StyleSheet, ActivityIndicator } from 'react-native';
import CustomToast from './CustomToast';

// options: [{ label, onPress: async () => {}, key }]
const MoreOptionsModal = ({ visible, onClose, options = [] }) => {
  const [loadingKey, setLoadingKey] = useState(null);
  const [toastData, setToastData] = useState(null);

  const handleOptionPress = async (option) => {
    if (!option.onPress) return;
    setLoadingKey(option.key);
    try {
      const result = await option.onPress();
      setToastData({ type: 'success', message: result?.message || 'Action successful!' });
    } catch (err) {
      setToastData({ type: 'error', message: err?.message || 'Action failed!' });
    } finally {
      setLoadingKey(null);
      onClose && onClose();
    }
  };

  return (
    <Modal
      transparent
      animationType="slide"
      visible={visible}
      onRequestClose={onClose}
    >
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>More Options</Text>
          {options.length === 0 && (
            <Text style={styles.modalText}>No options available</Text>
          )}
          {options.map(option => (
            <TouchableOpacity
              key={option.key}
              style={styles.modalItem}
              onPress={() => handleOptionPress(option)}
              disabled={!!loadingKey}
            >
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text style={styles.modalText}>{option.label}</Text>
                {loadingKey === option.key && (
                  <ActivityIndicator size="small" color="#333" style={{ marginLeft: 10 }} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={() => setToastData(null)}
          />
        )}
      </Pressable>
    </Modal>
  );
};

export default MoreOptionsModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContent: {
    backgroundColor: '#fff',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 12,
  },
  modalItem: {
    paddingVertical: 12,
  },
  modalText: {
    fontSize: 15,
    color: '#000',
  },
});
