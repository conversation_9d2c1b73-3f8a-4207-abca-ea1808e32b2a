import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import { fontFamilies } from '../../constant/Font';
import { useSelector } from 'react-redux';

const DashBoardTabs = () => {
    const [type, setType] = useState(2)
    const navigation = useNavigation()
    return (
        <>
        <View style={{
            justifyContent: 'flex-end',
            flexDirection: 'row',
            gap: 16,
            alignItems: 'center',
        }}>
            <View style={{flexDirection:'row', gap: 15, flex: 0.6}}>
            <TouchableOpacity onPress={() => setType(1)}>

                <Text style={{ color: type === 1 ? Color.Secondary : Color.inputBg,
                     fontSize: 15,
                      fontFamily:fontFamilies.POPPINS.extraBold }}>Following</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setType(2)}>
                <Text style={{ color: type === 2 ? Color.Secondary : Color.Primary, 
                    fontSize: 15,
                    fontFamily:fontFamilies.POPPINS.extraBold }}>For You</Text>
            </TouchableOpacity >
            </View>
            <TouchableOpacity onPress={() => navigation.navigate('SearchScreen')}>
                <Icon name="search" style={styles.search} size={25} color={Color.Secondary} />
            </TouchableOpacity>
        </View>

        {/* {type === 2 ? <Dashboard/> : <SettingScreen/>} */}
        </>
    )
}

export default DashBoardTabs

const styles = StyleSheet.create({
    search: {
        marginLeft: '4%'
    }
})