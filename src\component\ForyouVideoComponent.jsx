// === ForyouVideoComponent.js ===

import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
  Image,
} from "react-native";
import Video from "react-native-video";
import Icon from "react-native-vector-icons/Ionicons";
import { useEffect, useRef, useState } from "react";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import { Color } from "../constant/color";
import LikeCommComponent from "./LikeCommComponent";

const { width: w, height: h } = Dimensions.get("window");

const ForyouVideoComponent = ({ item, ProfileImage, musicImage, isPlay, items, onDeleteVideo, mediaType = 'video' }) => {
  const videoRef = useRef(null);
  const [showFull, setShowFull] = useState(false);
  const [paused, setPaused] = useState(!isPlay);
  const isFocused = useIsFocused();
  const navigation = useNavigation();

  useEffect(() => {
    setPaused(!isPlay);
  }, [isPlay]);

  useEffect(() => {
    if (!isFocused) {
      setPaused(true);
    } else {
      setPaused(!isPlay);
    }
  }, [isFocused, isPlay]);

  const togglePlayPause = () => {
    setPaused((prev) => !prev);
  };

const renderParsedText = () => {
  const words = items?.description?.split(" ") || [];
  const sliced = showFull ? words : words.slice(0, 12);

  return (
    <Text style={styles.descriptionText}>
      {sliced.map((word, index) => {
        const isHash = word.startsWith("#");
        return (
          <Text
            key={index}
            style={{ color: isHash ? "skyblue" : Color.Secondary }}
            onPress={() => {
              if (isHash) {
                navigation.navigate("SearchScreen", { tag: word.replace("#", "") });
              }
            }}
          >
            {word + " "}
          </Text>
        );
      })}

      {words.length > 12 && (
        <Text
          style={{ color: 'gray', fontWeight: 'bold' }}
          onPress={() => setShowFull(prev => !prev)}
        >
          {showFull ? " See less" : "... See more"}
        </Text>
      )}
    </Text>
  );
};

  const renderMedia = () => {
    if (mediaType === 'image') {
      return (
        <TouchableWithoutFeedback onPress={() => console.log('Image tapped')}>
          <Image
            source={typeof item === 'object' && item !== null ? item : { uri: typeof item === 'string' ? item : '' }}
            style={StyleSheet.absoluteFill}
            resizeMode="cover"
          />
        </TouchableWithoutFeedback>
      );
    } else {
      return (
        <>
          <TouchableWithoutFeedback onPress={togglePlayPause}>
            <Video
              ref={videoRef}
              source={typeof item === 'object' && item !== null ? item : { uri: typeof item === 'string' ? item : '' }}
              style={StyleSheet.absoluteFill}
              resizeMode="contain"
              repeat
              paused={paused}
            />
          </TouchableWithoutFeedback>

          {paused && (
            <View style={styles.centerIcon}>
              <Icon name="play-circle" size={64} color={Color.Secondary} />
            </View>
          )}
        </>
      );
    }
  };

  return (
    <View style={styles.container}>
      {renderMedia()}

      <View style={styles.overlayTextContainer}>
        <Text style={styles.title}>{items?.tittle}</Text>
        {renderParsedText()}
      </View>

      <LikeCommComponent
        ProfileImage={ProfileImage}
        musicImage={musicImage}
        videoData={{
          id: items?.id,
          videoUrl: typeof item === 'object' && item !== null && item.uri ? item.uri : 
                   typeof item === 'string' ? item : '',
          title: items?.tittle,
          description: items?.description,
          mediaType: mediaType
        }}
        onDeleteVideo={onDeleteVideo}
      />
    </View>
  );
};

export default ForyouVideoComponent;

const styles = StyleSheet.create({
  container: {
    width: w,
    height: h,
    backgroundColor: "black",
    position: "relative",
  },
  centerIcon: {
    position: "absolute",
    top: "45%",
    left: "50%",
    transform: [{ translateX: -32 }, { translateY: -32 }],
    justifyContent: "center",
    alignItems: "center",
  },
  overlayTextContainer: {
    position: "absolute",
    bottom: "14%",
    left: 15,
    width: "75%",
  },
  title: {
    color: Color.Secondary,
    fontWeight: "600",
    fontSize: 16,
    marginBottom: 4,
  },
  // descriptionText: {
  //   color: Color.Secondary,
  //   flexDirection: "row",
  //   flexWrap: "wrap",
  // },
  descriptionText: {
  color: Color.Secondary,
  flexDirection: "row",
  flexWrap: "wrap",
  lineHeight: 20,
},

});