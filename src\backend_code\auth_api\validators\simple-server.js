console.log('Starting simple server...');

const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

console.log('Express loaded');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI || 'mongodb+srv://app:<EMAIL>/?retryWrites=true&w=majority&appName=app')
  .then(() => console.log('MongoDB connected successfully'))
  .catch(err => console.error('MongoDB connection error:', err));

// User Schema
const userSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  phone: { type: String, required: true },
  address: { type: String, required: true },
  isVerified: { type: Boolean, default: false },
  profileImage: { type: String, default: null },
  createdAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

const app = express();

console.log('App created');

// Middleware
app.use(cors());
app.use(express.json());

console.log('Middleware added');

// Test route
app.get('/', (req, res) => {
  console.log('Root route hit');
  res.json({ 
    message: 'Simple server is working!',
    timestamp: new Date().toISOString()
  });
});

// Register route
app.post('/api/auth/register', async (req, res) => {
  console.log('Register route hit:', req.body);

  try {
    const { firstName, lastName, email, password, phone, address } = req.body;

    // Simple validation
    if (!firstName || !lastName || !email || !password || !phone || !address) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email'
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = new User({
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      email,
      password: hashedPassword,
      phone,
      address,
      isVerified: false
    });

    // Save user to database
    const savedUser = await newUser.save();

    // Generate JWT token
    const token = jwt.sign(
      { id: savedUser._id },
      process.env.JWT_SECRET || 'your_secret_key_change_this_in_production',
      { expiresIn: '7d' }
    );

    // Return success response
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token: token,
      user: {
        id: savedUser._id,
        name: savedUser.name,
        email: savedUser.email,
        phone: savedUser.phone,
        address: savedUser.address,
        isVerified: savedUser.isVerified
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

// Login route
app.post('/api/auth/login', (req, res) => {
  console.log('Login route hit:', req.body);
  
  const { email, password } = req.body;
  
  // Simple validation
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }
  
  // Mock response
  res.json({
    success: true,
    message: 'Login successful (Mock)',
    token: 'mock-jwt-token-' + Date.now(),
    user: {
      id: 'mock-id-123',
      name: 'Test User',
      email: email,
      phone: '1234567890',
      address: '123 Test Street',
      isVerified: true,
      profileImage: null
    }
  });
});

const PORT = 3001;

console.log('About to start server on port', PORT);

app.listen(PORT, () => {
  console.log(`🚀 Simple Server running on port ${PORT}`);
  console.log(`📍 Server URL: http://localhost:${PORT}`);
  console.log(`📋 Register URL: http://localhost:${PORT}/api/auth/register`);
  console.log(`📋 Login URL: http://localhost:${PORT}/api/auth/login`);
  console.log('✅ Server started successfully!');
});

console.log('Server setup complete');
