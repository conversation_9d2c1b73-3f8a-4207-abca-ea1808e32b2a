import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';

const { width, height } = Dimensions.get('window');

const CustomSplashScreen = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      ),
    ]).start();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor="#333399" barStyle="light-content" />
      
      <LinearGradient
        colors={['#333399', '#ff00cc']}
        style={styles.gradient}
      >
        {/* Background circles */}
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />
        <View style={styles.circleCenter} />

        {/* Main content */}
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* App Icon/Logo */}
          <Animated.View
            style={[
              styles.iconContainer,
              {
                transform: [{ rotate: spin }],
              },
            ]}
          >
            <Icon name="play-circle" size={80} color={Color.Secondary} />
          </Animated.View>

          {/* App Name */}
          <Text style={styles.appName}>MyPortfolio</Text>
          <Text style={styles.tagline}>Share Your Story</Text>

          {/* Loading indicator */}
          <View style={styles.loadingContainer}>
            <View style={styles.loadingBar}>
              <Animated.View
                style={[
                  styles.loadingProgress,
                  {
                    opacity: fadeAnim,
                  },
                ]}
              />
            </View>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        </Animated.View>

        {/* Bottom text */}
        <Animated.View
          style={[
            styles.bottomContainer,
            {
              opacity: fadeAnim,
            },
          ]}
        >
          <Text style={styles.versionText}>Version 1.0.0</Text>
          <Text style={styles.copyrightText}>© 2024 MyPortfolio</Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  circleTopLeft: {
    position: 'absolute',
    top: -100,
    left: -100,
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -80,
    right: -80,
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  circleCenter: {
    position: 'absolute',
    top: height * 0.3,
    right: -50,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255,255,255,0.08)',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 30,
    padding: 20,
    borderRadius: 60,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  appName: {
    fontSize: 32,
    fontFamily: fontFamilies.POPPINS.extraBold,
    color: Color.Secondary,
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: 1,
  },
  tagline: {
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.regular,
    color: 'rgba(255,255,255,0.8)',
    marginBottom: 50,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  loadingContainer: {
    alignItems: 'center',
    width: width * 0.6,
  },
  loadingBar: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 12,
  },
  loadingProgress: {
    height: '100%',
    width: '100%',
    backgroundColor: Color.Secondary,
    borderRadius: 2,
  },
  loadingText: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.medium,
    color: 'rgba(255,255,255,0.7)',
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 50,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.regular,
    color: 'rgba(255,255,255,0.6)',
    marginBottom: 4,
  },
  copyrightText: {
    fontSize: 11,
    fontFamily: fontFamilies.POPPINS.regular,
    color: 'rgba(255,255,255,0.5)',
  },
});

export default CustomSplashScreen;
