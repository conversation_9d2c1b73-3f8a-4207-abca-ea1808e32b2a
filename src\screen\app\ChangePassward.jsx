import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    SafeAreaView,
    ScrollView,
    ActivityIndicator,
} from 'react-native';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import Icon from 'react-native-vector-icons/Ionicons';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import CustomInput from '../../component/CustomInput';
import { useDispatch, useSelector } from 'react-redux';
import { changePassword } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';

const ChangePassword = () => {
    const navigation = useNavigation();
    const dispatch = useDispatch();
    const { loading } = useSelector(state => state.auth || {});

    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [errors, setErrors] = useState({});
    const [toastData, setToastData] = useState(null);

    const showToast = (type, message) => {
        setToastData({ type, message });
    };

    const hideToast = () => {
        setToastData(null);
    };

    const handleSubmit = async () => {
        let tempErrors = {};

        if (!oldPassword) tempErrors.oldPassword = 'Current password is required';
        if (!newPassword) tempErrors.newPassword = 'New password is required';
        if (!confirmPassword) tempErrors.confirmPassword = 'Confirm password is required';

        // Password validation
        if (newPassword && newPassword.length < 8) {
            tempErrors.newPassword = 'Password must be at least 8 characters';
        }
        if (newPassword && !/[^A-Za-z0-9]/.test(newPassword)) {
            tempErrors.newPassword = 'Password must contain at least one special character';
        }
        if (newPassword && confirmPassword && newPassword !== confirmPassword) {
            tempErrors.confirmPassword = 'Passwords do not match';
        }

        setErrors(tempErrors);

        if (Object.keys(tempErrors).length === 0) {
            try {
                console.log('Attempting to change password...');

                const result = await changePassword(oldPassword, newPassword)(dispatch);

                console.log('Change password result:', result);

                if (result.success) {
                    showToast('success', 'Password changed successfully!');
                    setTimeout(() => {
                        navigation.goBack();
                    }, 1500);
                } else {
                    showToast('error', result.message || 'Failed to change password');
                }
            } catch (error) {
                console.error('Change password error:', error);
                showToast('error', 'Failed to change password. Please try again.');
            }
        } else {
            showToast('error', 'Please fix the errors below');
        }
    };

    return (
        <LinearGradient colors={['#333399', '#ff00cc']} style={styles.container}>
            <SafeAreaView style={{ flex: 1 }}>
                <ScrollView contentContainerStyle={styles.inner}>
                    <View style={styles.header}>
                        <TouchableOpacity onPress={() => navigation.goBack()}>
                            <Icon name="arrow-back" size={24} color={Color.Secondary} />
                        </TouchableOpacity>
                        <Text style={styles.headerTitle}>Privacy Settings</Text>
                        <View style={{ width: 24 }} />
                    </View>

                    <View style={styles.overlay}>
                        <CustomInput
                            label="Old Password"
                            value={oldPassword}
                            onChangeText={setOldPassword}
                            placeholder="Enter old password"
                            secureTextEntry={true}
                            iconLeft="lock-closed"
                            error={errors.oldPassword}
                        />

                        <CustomInput
                            label="New Password"
                            value={newPassword}
                            onChangeText={setNewPassword}
                            placeholder="Enter new password"
                            secureTextEntry={true}
                            iconLeft="lock-closed"
                            error={errors.newPassword}
                        />

                        <CustomInput
                            label="Confirm Password"
                            value={confirmPassword}
                            onChangeText={setConfirmPassword}
                            placeholder="Confirm new password"
                            secureTextEntry={true}
                            iconLeft="lock-closed"
                            error={errors.confirmPassword}
                        />
                    </View>

                    <TouchableOpacity
                        onPress={handleSubmit}
                        style={[styles.button, loading && styles.buttonDisabled]}
                        disabled={loading}
                    >
                        {loading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator size="small" color="#fff" />
                                <Text style={[styles.buttonText, { marginLeft: 10 }]}>Changing...</Text>
                            </View>
                        ) : (
                            <Text style={styles.buttonText}>Change Password</Text>
                        )}
                    </TouchableOpacity>
                </ScrollView>

                {toastData && (
                    <CustomToast
                        type={toastData.type}
                        message={toastData.message}
                        onHide={hideToast}
                    />
                )}
            </SafeAreaView>
        </LinearGradient>
    );
};

export default ChangePassword;

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    inner: {
        padding: 20,
    },
    backIcon: {
        marginBottom: 10,
    },
    header: {
        marginTop: '7%',
        marginHorizontal: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontFamily: fontFamilies.POPPINS.extraBold,
        color: Color.Secondary,
        textAlign: 'center',
    },

    overlay: {
        marginTop: 20,

    },
    button: {
        backgroundColor: 'rgba(255,255,255,0.2)',
        paddingVertical: 14,
        borderRadius: 10,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.4)',
        marginTop: 10,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontFamily: fontFamilies.POPPINS.bold,
        textAlign: 'center',
    },
    buttonDisabled: {
        backgroundColor: 'rgba(255,255,255,0.08)',
        borderColor: 'rgba(255,255,255,0.15)',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
});
