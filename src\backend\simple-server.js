const express = require('express');
const http = require('http');
const path = require('path');

console.log('🚀 Starting Simple TikTok Server...');

const app = express();
const server = http.createServer(app);

// Manual CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }
  next();
});

// JSON parser middleware
app.use(express.json());

console.log('✅ Middleware configured');

// Mock Database
let users = [
  {
    id: 'user-1',
    firstName: 'John',
    lastName: '<PERSON><PERSON>',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '12345678!',
    phone: '1234567890',
    address: '123 Main St',
    bio: 'TikTok Creator 🎬',
    profileImage: null,
    isVerified: true,
    isProfileComplete: true,
    followersCount: 1250,
    followingCount: 890,
    postsCount: 45,
    createdAt: new Date().toISOString()
  },
  {
    id: 'user-2',
    firstName: 'Jane',
    lastName: 'Smith',
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: '12345678!',
    phone: '0987654321',
    address: '456 Oak Ave',
    bio: 'Dance & Music Lover 💃🎵',
    profileImage: null,
    isVerified: true,
    isProfileComplete: true,
    followersCount: 2340,
    followingCount: 567,
    postsCount: 78,
    createdAt: new Date().toISOString()
  }
];

let posts = [
  {
    id: 'post-1',
    userId: 'user-1',
    description: 'Amazing dance moves! 💃 #dance #viral #fyp',
    music: 'Trending Song 2024',
    privacy: 'everyone',
    video: 'sample-video-1.mp4',
    image: null,
    likesCount: 1250,
    commentsCount: 89,
    sharesCount: 45,
    viewsCount: 15600,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'post-2',
    userId: 'user-2',
    description: 'Cooking hack that will blow your mind! 🍳 #cooking #lifehack #food',
    music: 'Chill Vibes',
    privacy: 'everyone',
    video: 'sample-video-2.mp4',
    image: null,
    likesCount: 890,
    commentsCount: 67,
    sharesCount: 23,
    viewsCount: 8900,
    createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
  }
];

let messages = [
  {
    id: 'message-1',
    senderId: 'user-1',
    receiverId: 'user-2',
    text: 'Hey! Love your latest video! 🔥',
    isRead: false,
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
  }
];

let notifications = [
  {
    id: 'notif-1',
    userId: 'user-1',
    fromUserId: 'user-2',
    type: 'like',
    postId: 'post-1',
    message: 'Jane Smith liked your post',
    isRead: false,
    createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString()
  }
];

// Helper functions
const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2, 9);
const findUser = (id) => users.find(u => u.id === id);

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root route accessed');
  res.json({ 
    message: 'Simple TikTok Server is working!',
    timestamp: new Date().toISOString(),
    status: 'OK',
    ip: '**************',
    port: 3001
  });
});

// Login API
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login API called:', req.body);
  
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  const user = users.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  res.json({
    success: true,
    message: 'Login successful',
    token: 'token-' + user.id,
    user: user
  });
});

// Register API
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register API called:', req.body);
  
  const { firstName, lastName, email, password, phone, address } = req.body;
  
  if (!firstName || !lastName || !email || !password || !phone || !address) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  const newUser = {
    id: generateId(),
    firstName,
    lastName,
    name: `${firstName} ${lastName}`,
    email,
    password,
    phone,
    address,
    bio: '',
    profileImage: null,
    isVerified: false,
    isProfileComplete: false,
    followersCount: 0,
    followingCount: 0,
    postsCount: 0,
    createdAt: new Date().toISOString()
  };

  users.push(newUser);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: 'token-' + newUser.id,
    user: newUser
  });
});

// Get Posts API
app.get('/api/posts', (req, res) => {
  console.log('📱 Get Posts API called');
  
  const postsWithUserData = posts.map(post => {
    const user = findUser(post.userId);
    
    return {
      ...post,
      user: {
        id: user?.id,
        name: user?.name,
        profileImage: user?.profileImage,
        isVerified: user?.isVerified
      },
      isLiked: false,
      videoUrl: post.video ? `/uploads/videos/${post.video}` : null,
      imageUrl: post.image ? `/uploads/images/${post.image}` : null
    };
  });

  res.json({
    success: true,
    posts: postsWithUserData
  });
});

// Send Message API
app.post('/api/chat/:userId', (req, res) => {
  console.log('💬 Send Message API called:', req.params.userId, req.body);
  
  const { userId: receiverId } = req.params;
  const { text } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const senderId = token?.replace('token-', '');
  
  const newMessage = {
    id: generateId(),
    senderId,
    receiverId,
    text,
    isRead: false,
    createdAt: new Date().toISOString()
  };

  messages.push(newMessage);

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    data: newMessage
  });
});

// Get Messages API
app.get('/api/chat/:userId', (req, res) => {
  console.log('💬 Get Messages API called:', req.params.userId);
  
  const { userId: otherUserId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');
  
  const conversation = messages.filter(m => 
    (m.senderId === currentUserId && m.receiverId === otherUserId) ||
    (m.senderId === otherUserId && m.receiverId === currentUserId)
  );

  res.json({
    success: true,
    messages: conversation,
    user: findUser(otherUserId)
  });
});

// Search Users API
app.get('/api/users/search', (req, res) => {
  console.log('🔍 Search Users API called:', req.query);

  const { q } = req.query;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  let filteredUsers = users;

  // If search query provided, filter users
  if (q && q.trim()) {
    const searchQuery = q.toLowerCase();
    filteredUsers = users.filter(user =>
      user.name.toLowerCase().includes(searchQuery) ||
      user.email.toLowerCase().includes(searchQuery) ||
      (user.bio && user.bio.toLowerCase().includes(searchQuery))
    );
  }

  // Remove current user from results
  filteredUsers = filteredUsers.filter(user => user.id !== currentUserId);

  res.json({
    success: true,
    users: filteredUsers
  });
});

// Follow/Unfollow User API
app.post('/api/users/:userId/follow', (req, res) => {
  console.log('👥 Follow User API called:', req.params.userId);

  const { userId: targetUserId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const targetUser = findUser(targetUserId);
  const currentUser = findUser(currentUserId);

  if (!targetUser || !currentUser) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // For simplicity, just return success
  // In real app, you'd manage followers/following relationships
  res.json({
    success: true,
    message: `Successfully followed ${targetUser.name}`,
    isFollowing: true
  });
});

const PORT = 3001;

server.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Server failed to start:', err);
    return;
  }
  console.log(`🚀 Simple TikTok Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`📍 Network: http://**************:${PORT}`);
  console.log('✅ Server started successfully!');
  console.log('');
  console.log('📋 Available APIs:');
  console.log('🔐 Auth: /api/auth/login, /api/auth/register');
  console.log('📱 Posts: /api/posts');
  console.log('💬 Chat: /api/chat/:userId');
});

console.log('📝 Simple server setup complete');
