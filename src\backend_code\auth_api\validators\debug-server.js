console.log('🚀 Starting debug server...');

const express = require('express');
const cors = require('cors');

console.log('✅ Express and CORS loaded');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

console.log('✅ Middleware configured');

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root route hit');
  res.json({ 
    message: 'Debug server is working!',
    timestamp: new Date().toISOString()
  });
});

// Simple login route for testing
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login route hit:', req.body);
  
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  // Mock successful login
  res.json({
    success: true,
    message: 'Login successful (<PERSON><PERSON>)',
    token: 'mock-token-' + Date.now(),
    user: {
      id: 'mock-id-123',
      name: 'Test User',
      email: email,
      phone: '1234567890',
      address: '123 Test Street'
    }
  });
});

const PORT = 3001;

console.log('🔧 About to start server on port', PORT);

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Debug Server running on port ${PORT}`);
  console.log(`📍 Server URL: http://localhost:${PORT}`);
  console.log(`📍 Network URL: http://*************:${PORT}`);
  console.log('✅ Server started successfully!');
});

console.log('📝 Server setup complete');
