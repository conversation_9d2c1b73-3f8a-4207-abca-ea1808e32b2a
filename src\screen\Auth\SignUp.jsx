import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  Dimensions,
  ScrollView,
  StatusBar,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import CustomInput from '../../component/CustomInput';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser } from '../../redux/action/AuthAction';
import Toast from 'react-native-simple-toast';
import CustomToast from '../../component/CustomToast';

const { width } = Dimensions.get('window');

const SignUp = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth || {});

  const [submitted, setSubmitted] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [toastData, setToastData] = useState(null); 

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const handleSignUp = async () => {
    setSubmitted(true);

    const emailValid = email.includes('@') && email.includes('.') && email.length > 5;
    const passwordValid = password.length >= 8 && /[^A-Za-z0-9]/.test(password);
    const confirmMatch = password === confirmPassword;

    if (
      !firstName ||
      !lastName ||
      !emailValid ||
      !address ||
      !phone ||
      !passwordValid ||
      !confirmMatch
    ) {
      // Alert.alert('Validation Error', 'Please fill all fields correctly');
      Toast.SHORT('Please fill all fields correctly', Toast.LONG)
      return;
    }

    try {
      const userData = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        email: email.trim().toLowerCase(),
        password: password,
        phone: phone.trim(),
        address: address.trim()
      };

      console.log('Attempting to register user:', userData);

      // Dispatch register action
      const result = await registerUser(userData)(dispatch);
      console.log('result', result)

      
      if (result.success) {
        showToast('success', 'Account created successfully!');
        navigation.navigate('CompleteProfile');
      } else {
        showToast('error', result.error || 'Something went wrong');

                  console.log('Registration result:', result);
      }
    } catch (error) {
      console.error('SignUp error:', error);
      Alert.alert('Error', 'Registration failed. Please try again.');
    }
  };

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  return (
    <>
    <SafeAreaView style={styles.background}>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
      <LinearGradient colors={['#333399', '#ff00cc']} style={styles.background}>
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />

<ScrollView>
        <View style={styles.overlay}>
          <View style={styles.glassWrapper}>
            {Platform.OS === 'ios' ? (
              <BlurView style={styles.glass} blurType="light" blurAmount={20} />
            ) : (
              <View style={[styles.glass, styles.androidGlass]} />
            )}

            <LinearGradient
              colors={['rgba(255,255,255,0.08)', 'rgba(255,255,255,0.02)']}
              style={StyleSheet.absoluteFill}
            />

            {/* <KeyboardAvoidingView behavior="padding"> */}
              <Text style={styles.title}>Sign Up</Text>

              <View style={{ flexDirection: 'row', gap: 5 }}>
                <View style={{ width: '48%' }}>
                  <CustomInput
                    label="First Name"
                    value={firstName}
                    onChangeText={setFirstName}
                    placeholder="First Name"
                    error={submitted && !firstName ? 'Required' : ''}
                  />
                </View>
                <View style={{ width: '48%' }}>
                  <CustomInput
                    label="Last Name"
                    value={lastName}
                    onChangeText={setLastName}
                    placeholder="Last Name"
                    error={submitted && !lastName ? 'Required' : ''}
                  />
                </View>
              </View>

              <CustomInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                placeholder="Email"
                iconLeft="mail-outline"
                keyboardType="email-address"
                error={
                  submitted && (!email
                    ? 'Required'
                    : !(email.includes('@') && email.includes('.') && email.length > 5)
                    ? 'Enter a valid email'
                    : '')
                }
              />

              <View style={{ flexDirection: 'row', gap: 5 }}>
                <View style={{ width: '48%' }}>
                  <CustomInput
                    label="Address"
                    value={address}
                    onChangeText={setAddress}
                    placeholder="Address"
                    iconLeft="home-outline"
                    error={submitted && !address ? 'Required' : ''}
                  />
                </View>
                <View style={{ width: '48%' }}>
                  <CustomInput
                    label="Phone"
                    value={phone}
                    onChangeText={setPhone}
                    placeholder="Phone"
                    keyboardType="phone-pad"
                    iconLeft="call-outline"
                    error={submitted && !phone ? 'Required' : ''}
                  />
                </View>
              </View>

              <CustomInput
                label="Password"
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                iconLeft="lock-closed-outline"
                iconRight="eye"
                error={
                  submitted && (!password
                    ? 'Password required'
                    : password.length < 8
                    ? 'Min 8 characters'
                    : !/[^A-Za-z0-9]/.test(password)
                    ? 'Must include special char'
                    : '')
                }
              />

              <CustomInput
                label="Confirm Password"
                placeholder="Confirm Password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
                iconLeft="lock-closed-outline"
                iconRight="eye"
                error={
                  submitted &&
                  (!confirmPassword
                    ? 'Confirm your password'
                    : confirmPassword !== password
                    ? 'Passwords do not match'
                    : '')
                }
              />

              {error && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                </View>
              )}

              <TouchableOpacity
                onPress={handleSignUp}
                style={[styles.button, loading && styles.buttonDisabled]}
                disabled={loading}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#fff" />
                    <Text style={[styles.buttonText, { marginLeft: 10 }]}>Creating Account...</Text>
                  </View>
                ) : (
                  <Text style={styles.buttonText}>Sign Up</Text>
                )}
              </TouchableOpacity>

              <View style={styles.footerRow}>
                <Text style={styles.forgot}>Already have an account?</Text>
                <TouchableOpacity onPress={handleLogin}>
                  <Text style={styles.SigninText}> Login</Text>
                </TouchableOpacity>
              </View>
            {/* </KeyboardAvoidingView> */}
          </View>
        </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>

     {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

export default SignUp;


const styles = StyleSheet.create({
    background: {
        flex: 1,
        justifyContent: 'center',
    },
    overlay: {
        margin: '30%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    circleTopLeft: {
        position: 'absolute',
        top: -60,
        left: -60,
        width: 180,
        height: 180,
        borderRadius: 90,
        backgroundColor: 'rgba(255,255,255,0.1)',
    },
    circleBottomRight: {
        position: 'absolute',
        bottom: -40,
        right: -40,
        width: 150,
        height: 150,
        borderRadius: 75,
        backgroundColor: 'rgba(255,255,255,0.15)',
    },
    glassWrapper: {
        width: width * 0.9,
        padding: 30,
        borderRadius: 25,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.2)',
        backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
    },
    glass: {
        ...StyleSheet.absoluteFillObject,
        borderRadius: 25,
    },
    androidGlass: {
        backgroundColor: 'rgba(255,255,255,0.1)',
    },
    title: {
        fontSize: 28,
        color: '#fff',
        marginBottom: 20,
        fontStyle: 'italic',
        fontFamily: fontFamilies.POPPINS.extraBold,
        textAlign: 'center',
    },
    input: {
        height: 45,
        width: '50%',
        borderColor: 'rgba(255,255,255,0.3)',
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: 15,
        color: '#fff',
        marginBottom: 15,
        backgroundColor: 'rgba(255,255,255,0.1)',
    },
    inputFull: {
        height: 45,
        borderColor: 'rgba(255,255,255,0.3)',
        borderWidth: 1,
        borderRadius: 10,
        paddingHorizontal: 15,
        color: '#fff',
        marginBottom: 15,
        backgroundColor: 'rgba(255,255,255,0.1)',
    },
    button: {
        backgroundColor: 'rgba(255,255,255,0.15)',
        paddingVertical: 12,
        borderRadius: 10,
        marginTop: 10,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.3)',
    },
    buttonDisabled: {
        backgroundColor: 'rgba(255,255,255,0.08)',
        borderColor: 'rgba(255,255,255,0.15)',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    errorContainer: {
        backgroundColor: 'rgba(255, 0, 0, 0.1)',
        borderWidth: 1,
        borderColor: 'rgba(255, 0, 0, 0.3)',
        borderRadius: 8,
        padding: 10,
        marginTop: 10,
    },
    errorText: {
        color: '#ff6b6b',
        fontSize: 14,
        fontFamily: fontFamilies.POPPINS.regular,
        textAlign: 'center',
    },
    buttonText: {
        color: '#fff',
        fontSize: 18,
        fontFamily: fontFamilies.POPPINS.bold,
        textAlign: 'center',
    },
    forgot: {
        fontFamily: fontFamilies.POPPINS.regular,
        color: Color.Secondary,
    },
    SigninText: {
        textDecorationLine: 'underline',
        color: '#00f',
        fontFamily: fontFamilies.POPPINS.regular,
    },
    footerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 15,
        justifyContent: 'center',
    },
});
