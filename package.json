{"name": "MyPortfolio", "version": "0.0.1", "private": true, "scripts": {"android": "npx react-native run-android", "ios": "npx react-native run-ios", "lint": "eslint .", "start": "npx react-native start", "test": "jest", "server": "node src/backend_code/auth_api/validators/server.jsx", "server:dev": "nodemon src/backend_code/auth_api/validators/server.jsx"}, "dependencies": {"@alirehman7141/react-native-audiowaveform": "^2.0.6", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "bcryptjs": "^3.0.2", "curved-bottom-navigation-bar": "^2.0.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jetifier": "^2.0.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.16.4", "multer": "^2.0.2", "nodemailer": "^7.0.5", "nodemon": "^3.1.10", "react": "18.3.1", "react-native": "^0.76.1", "react-native-audio-recorder-player": "^3.6.14", "react-native-curved-bottom-bar": "^3.5.1", "react-native-dropdown-picker": "^5.4.6", "react-native-element-dropdown": "^2.12.4", "react-native-emoji-selector": "^0.2.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.26.0", "react-native-image-crop-picker": "^0.50.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-otp-textinput": "^1.1.7", "react-native-permissions": "^5.4.1", "react-native-progress": "^5.0.1", "react-native-render-html": "^6.3.4", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^5.5.0", "react-native-simple-toast": "^3.3.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.12.0", "react-native-svg-transfo    npx react-native start --reset-cachermer": "^1.5.1", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.15.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-devtools-extension": "^2.13.9", "redux-thunk": "^3.1.0", "rn-emoji-keyboard": "^1.7.0", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}