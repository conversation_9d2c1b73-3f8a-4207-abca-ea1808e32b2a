const http = require('http');
const url = require('url');

console.log('🚀 Starting Minimal Server...');

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${method} ${path}`);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Root route
  if (path === '/' && method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'Minimal Server is working!',
      timestamp: new Date().toISOString()
    }));
    return;
  }

  // Register API
  if (path === '/api/auth/register' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('📝 Register API called:', data);

        const { firstName, lastName, email, password, phone, address } = data;

        if (!firstName || !lastName || !email || !password || !phone || !address) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'All fields are required'
          }));
          return;
        }

        res.writeHead(201);
        res.end(JSON.stringify({
          success: true,
          message: 'User registered successfully',
          token: 'mock-token-' + Date.now(),
          user: {
            id: 'user-' + Date.now(),
            name: `${firstName} ${lastName}`,
            firstName,
            lastName,
            email,
            phone,
            address,
            isVerified: false,
            isProfileComplete: false
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Login API
  if (path === '/api/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔐 Login API called:', data);

        const { email, password } = data;

        if (!email || !password) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Email and password are required'
          }));
          return;
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Login successful',
          token: 'mock-token-' + Date.now(),
          user: {
            id: 'user-123',
            name: 'Test User',
            email: email,
            phone: '1234567890',
            address: '123 Test Street',
            isVerified: true,
            isProfileComplete: true
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Complete Profile API
  if (path === '/api/auth/complete-profile' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('👤 Complete Profile API called:', data);

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Profile completed successfully',
          user: {
            id: 'user-123',
            firstName: 'Test',
            lastName: 'User',
            name: 'Test User',
            email: '<EMAIL>',
            phone: '1234567890',
            address: '123 Test Street',
            bio: data.bio || 'Test bio',
            gender: data.gender || 'male',
            dateOfBirth: data.dateOfBirth || new Date().toISOString(),
            interests: data.interests || [],
            profileImage: data.profileImage || null,
            isProfileComplete: true,
            isVerified: true
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Forgot Password API
  if (path === '/api/auth/forgot-password' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔑 Forgot Password API called:', data);

        const { email } = data;

        if (!email) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Email is required'
          }));
          return;
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'OTP sent to your email',
          otp: '123456' // In real app, don't send OTP in response
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Verify OTP API
  if (path === '/api/auth/verify-otp' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔢 Verify OTP API called:', data);

        const { email, otp } = data;

        if (!email || !otp) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Email and OTP are required'
          }));
          return;
        }

        if (otp === '123456') {
          res.writeHead(200);
          res.end(JSON.stringify({
            success: true,
            message: 'OTP verified successfully'
          }));
        } else {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Invalid OTP'
          }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Reset Password API
  if (path === '/api/auth/reset-password' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔐 Reset Password API called:', data);

        const { email, otp, newPassword } = data;

        if (!email || !otp || !newPassword) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Email, OTP and new password are required'
          }));
          return;
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Password reset successfully'
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Update Profile API
  if (path === '/api/auth/profile' && method === 'PUT') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('📝 Update Profile API called:', data);

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Profile updated successfully',
          user: {
            id: 'user-123',
            firstName: data.firstName || 'Test',
            lastName: data.lastName || 'User',
            name: `${data.firstName || 'Test'} ${data.lastName || 'User'}`,
            email: '<EMAIL>',
            phone: data.phone || '1234567890',
            address: data.address || '123 Test Street',
            bio: data.bio || 'Updated bio'
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Change Password API
  if (path === '/api/auth/change-password' && method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('🔑 Change Password API called:', data);

        const { currentPassword, newPassword } = data;

        if (!currentPassword || !newPassword) {
          res.writeHead(400);
          res.end(JSON.stringify({
            success: false,
            message: 'Current password and new password are required'
          }));
          return;
        }

        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Password changed successfully'
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Invalid JSON' }));
      }
    });
    return;
  }

  // Logout API
  if (path === '/api/auth/logout' && method === 'POST') {
    console.log('🚪 Logout API called');

    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Logged out successfully'
    }));
    return;
  }

  // 404 for other routes
  res.writeHead(404);
  res.end(JSON.stringify({ message: 'Not found' }));
});

const PORT = 3001;

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Minimal Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`📍 Network: http://***************:${PORT}`);
  console.log('✅ Server started successfully!');
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

console.log('📝 Server setup complete');
