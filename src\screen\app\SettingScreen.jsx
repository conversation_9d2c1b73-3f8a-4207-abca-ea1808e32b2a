import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-community/blur';
import OTPTextInput from 'react-native-otp-textinput';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';

const SettingScreen = () => {
    const navigation = useNavigation()
  return (
    <LinearGradient
  colors={['#333399', '#ff00cc']}
  style={styles.background}
>
        <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />
  <View style={styles.circleTopLeft} />
  <View style={styles.circleBottomRight} />

  <View style={styles.headerContainer}>
    {Platform.OS === 'ios' && (
      <BlurView style={styles.headerGlass} blurType="light" blurAmount={20} />
    )}
    <LinearGradient
      colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
      style={styles.headerGlass}
    >
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Icon name="arrow-back" size={24} color="#fff" />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Settings</Text>
    </LinearGradient>
  </View>

  {/* Main Glass Card */}
  <View style={styles.overlay}>
    {Platform.OS === 'ios' && (
      <BlurView style={styles.glass} blurType="light" blurAmount={20} />
    )}

    <LinearGradient
      colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
      style={styles.glass}
    >
      <Text style={styles.title}>Account</Text>
     <View>
      <TouchableOpacity onPress={() => navigation.navigate('ChangePassward')} style={styles.settingItemContainer}>
        <Icon name="lock-closed-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Change Password</Text>
      </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.navigate('EditProfile')} style={styles.settingItemContainer}>
        <Icon name="person-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Profile Edit</Text>
      </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('PrivateScreen')} style={styles.settingItemContainer}>
        <Icon name="lock-closed-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Private</Text>
      </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('PolicyScreen')} style={styles.settingItemContainer}>
        <Icon name="lock-closed-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Trems & Conditions</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={() => navigation.navigate('NotificationPage')} style={styles.settingItemContainer}>
        <Icon name="notifications-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Notification</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.settingItemContainer}>
        <Icon name="log-out-outline" size={24} color="#fff" />
         <Text style={styles.settingItemText}>Logout</Text>
      </TouchableOpacity>
      
     </View>
    </LinearGradient>
  </View>
</LinearGradient>

  );
};

export default SettingScreen;

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
  },
  overlay: {
    margin: 20,
    overflow: 'hidden',
  },
  circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  glass: {
    padding: 30,
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  title: {
    fontSize: 26,
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.extraBold,
    textAlign: 'center',
    marginBottom: 10,
  },
  settingItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderColor: 'rgba(255,255,255,0.1)',
  },
  settingItemText: {
    color: Color.Secondary,
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  footerText: {
    color: '#ccc',
    textAlign: 'center',
    marginTop: 15,
    fontFamily: fontFamilies.POPPINS.regular,
  },
    headerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 90,
    paddingTop: Platform.OS === 'android' ? 20 : 40,
    paddingHorizontal: 20,
    zIndex: 10,
  },
  headerGlass: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: 10,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.08)' : 'transparent',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  backButton: {
    padding: 6,
    marginRight: 10,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
  },

});
