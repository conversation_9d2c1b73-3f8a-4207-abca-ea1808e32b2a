import {
  SET_LOADING,
  SET_ERROR,
  CLEAR_ERROR,
  SET_SEARCH_RESULTS,
  SET_USER_PROFILE,
  SET_FOLLOWERS,
  SET_FOLLOWING
} from '../action/UserAction';

const initialState = {
  searchResults: [],
  userProfile: null,
  followers: [],
  following: [],
  loading: false,
  error: null
};

const UserReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case SET_SEARCH_RESULTS:
      return {
        ...state,
        searchResults: action.payload,
        loading: false,
        error: null
      };

    case SET_USER_PROFILE:
      return {
        ...state,
        userProfile: action.payload,
        loading: false,
        error: null
      };

    case SET_FOLLOWERS:
      return {
        ...state,
        followers: action.payload,
        loading: false,
        error: null
      };

    case SET_FOLLOWING:
      return {
        ...state,
        following: action.payload,
        loading: false,
        error: null
      };

    default:
      return state;
  }
};

export default UserReducer;
