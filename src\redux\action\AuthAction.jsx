import AsyncStorage from '@react-native-async-storage/async-storage';
const BASE_URL = 'http://192.168.100.150:3002/api/auth';

// Helper function to get auth headers
const getAuthHeaders = async () => {
  const token = await AsyncStorage.getItem('authToken');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export const AUTH_LOADING = 'AUTH_LOADING';
export const AUTH_SUCCESS = 'AUTH_SUCCESS';
export const AUTH_ERROR = 'AUTH_ERROR';
export const REGISTER_SUCCESS = 'REGISTER_SUCCESS';
export const LOGIN_SUCCESS = 'LOGIN_SUCCESS';
export const LOGOUT_SUCCESS = 'LOGOUT_SUCCESS';
export const CLEAR_ERROR = 'CLEAR_ERROR';

export const setLoading = (loading) => ({
  type: AUTH_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: AUTH_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

export const setAuthSuccess = (user, token) => ({
  type: AUTH_SUCCESS,
  payload: { user, token }
});

// Register User Action
export const registerUser = (userData) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      console.log('Registering user with data:', userData);

      const response = await fetch(`${BASE_URL}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      });

      const result = await response.json();
      console.log('Register API response:', result?.status);

      if (result.success) {
        // Save token to AsyncStorage
        if (result.token) {
          await AsyncStorage.setItem('authToken', result.token);
          await AsyncStorage.setItem('userData', JSON.stringify(result.user));
        }

        dispatch({
          type: REGISTER_SUCCESS,
          payload: {
            user: result.user,
            token: result.token,
            message: result.message
          }
        });

        dispatch(setLoading(false));
        return { success: true, data: result };
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (error) {
      console.error('Register error:', error);
      dispatch(setError(error.message || 'Registration failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Login User Action
export const loginUser = (email, password) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      console.log('Logging in user:', email);

      const response = await fetch(`${BASE_URL}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password })
      });

      const result = await response.json();
      console.log('Login API response:', result);

      if (result.success) {
        // Save token to AsyncStorage
        if (result.token) {
          await AsyncStorage.setItem('authToken', result.token);
          await AsyncStorage.setItem('userData', JSON.stringify(result.user));
        }

        dispatch({
          type: LOGIN_SUCCESS,
          payload: {
            user: result.user,
            token: result.token,
            message: result.message
          }
        });

        dispatch(setLoading(false));
        return { success: true, data: result };
      } else {
        throw new Error(result.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      dispatch(setError(error.message || 'Login failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Forgot Password Action
export const forgotPassword = (email) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const response = await fetch(`${BASE_URL}/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      });

      const result = await response.json();
      console.log('Forgot password API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Forgot password error:', error);
      dispatch(setError(error.message || 'Forgot password failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Verify OTP Action
export const verifyOTP = (email, otp) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const response = await fetch(`${BASE_URL}/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp })
      });

      const result = await response.json();
      console.log('Verify OTP API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Verify OTP error:', error);
      dispatch(setError(error.message || 'OTP verification failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Reset Password Action
export const resetPassword = (email, otp, newPassword) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const response = await fetch(`${BASE_URL}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp, newPassword })
      });

      const result = await response.json();
      console.log('Reset password API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Reset password error:', error);
      dispatch(setError(error.message || 'Password reset failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Profile Action
export const getProfile = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/profile`, {
        method: 'GET',
        headers: await getAuthHeaders()
      });

      const result = await response.json();
      console.log('Get profile API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, user: result.user };
    } catch (error) {
      console.error('Get profile error:', error);
      dispatch(setError(error.message || 'Failed to get profile'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Update Profile Action
export const updateProfile = (profileData) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/profile`, {
        method: 'PUT',
        headers: await getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      const result = await response.json();
      console.log('Update profile API response:', result);

      if (result.success) {
        // Update stored user data
        await AsyncStorage.setItem('userData', JSON.stringify(result.user));

        dispatch({
          type: AUTH_SUCCESS,
          payload: {
            user: result.user,
            token: token
          }
        });
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Update profile error:', error);
      dispatch(setError(error.message || 'Profile update failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Complete Profile Action
export const completeProfile = (profileData) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/complete-profile`, {
        method: 'POST',
        headers: await getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      const result = await response.json();
      console.log('Complete profile API response:', result);

      if (result.success) {
        // Update stored user data
        await AsyncStorage.setItem('userData', JSON.stringify(result.user));

        dispatch({
          type: AUTH_SUCCESS,
          payload: {
            user: result.user,
            token: token
          }
        });
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Complete profile error:', error);
      dispatch(setError(error.message || 'Profile completion failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Change Password Action
export const changePassword = (currentPassword, newPassword) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/change-password`, {
        method: 'POST',
        headers: await getAuthHeaders(),
        body: JSON.stringify({ currentPassword, newPassword })
      });

      const result = await response.json();
      console.log('Change password API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, message: result.message };
    } catch (error) {
      console.error('Change password error:', error);
      dispatch(setError(error.message || 'Password change failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Logout User Action
export const logoutUser = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));

      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        // Call logout API
        await fetch(`${BASE_URL}/logout`, {
          method: 'POST',
          headers: await getAuthHeaders()
        });
      }

      // Remove token from AsyncStorage
      await AsyncStorage.removeItem('authToken');
      await AsyncStorage.removeItem('userData');

      dispatch({
        type: LOGOUT_SUCCESS
      });

      dispatch(setLoading(false));
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      dispatch(setError('Logout failed'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Check if user is logged in
export const checkAuthStatus = () => {
  return async (dispatch) => {
    try {
      console.log('Checking auth status...');
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');

      console.log('Auth check - Token exists:', !!token, 'User data exists:', !!userData);

      if (token && userData) {
        try {
          const user = JSON.parse(userData);
          console.log('Restoring user from AsyncStorage:', user);
          
          // Dispatch auth success to update Redux state
          dispatch({
            type: AUTH_SUCCESS,
            payload: { user, token }
          });
          
          return { success: true, user, token };
        } catch (parseError) {
          console.error('Error parsing user data:', parseError);
          // Clear corrupted data
          await AsyncStorage.multiRemove(['authToken', 'userData']);
          return { success: false, error: 'Corrupted user data' };
        }
      } else {
        console.log('No auth data found in AsyncStorage');
        return { success: false };
      }
    } catch (error) {
      console.error('Auth check error:', error);
      return { success: false, error: error.message };
    }
  };
};