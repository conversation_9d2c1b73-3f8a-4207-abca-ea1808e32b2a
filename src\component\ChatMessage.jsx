import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
// AudioWaveform replaced with simple audio playback for APK build compatibility
// import WaveForm from '@alirehman7141/react-native-audiowaveform';
import Video from 'react-native-video';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import { Color } from '../constant/color';
import MediaGalleryModal from './MediaViwerModal';

const audioRecorderPlayer = new AudioRecorderPlayer();

const ChatMessage = ({
  message,
  isSender,
  onLongPress,
  editingMessage,
  editText,
  onEditTextChange,
  onSaveEdit,
  onCancelEdit,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingUri, setCurrentPlayingUri] = useState(null);
  const [duration, setDuration] = useState('0:00');
  const playbackListener = useRef(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);

  const stopPlayer = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      if (playbackListener.current) {
        audioRecorderPlayer.removePlayBackListener(playbackListener.current);
        playbackListener.current = null;
      }
      setIsPlaying(false);
      setCurrentPlayingUri(null);
    } catch (e) {
      console.warn('Stop error', e);
    }
  };

  const onPlay = async (uri) => {
    try {
      if (isPlaying) {
        await stopPlayer();
        if (currentPlayingUri === uri) return;
      }

      await audioRecorderPlayer.startPlayer(uri);
      setCurrentPlayingUri(uri);
      setIsPlaying(true);

      playbackListener.current = audioRecorderPlayer.addPlayBackListener((e) => {
        const totalSeconds = Math.floor(e.duration / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
         setDuration(`${minutes}:${seconds < 10 ? '0' : ''}${seconds}`);

        if (e.currentPosition >= e.duration - 100) {
          stopPlayer();
        }
      });
    } catch (error) {
      console.error('Play Error:', error);
    }
  };

  useEffect(() => {
    return () => {
      stopPlayer();
    };
  }, []);

const handleLongPress = () => {
  if (onLongPress) {
    onLongPress();
  }
}

  const openGalleryViewer = (index) => {
    setSelectedMediaIndex(index);
    setModalVisible(true);
  };

  console.log('message.replyTo.text============>', message)

  const renderMedia = () => {
    if (!message.media || message.media.length === 0) return null;

    const renderItem = (item, index) => {
      const isImage = item.type === 'image';
      const isVideo = item.type === 'video';
      const isAudio = item.type === 'audio';

      if (isImage || isVideo) {
        return (
          <TouchableOpacity key={index} onPress={() => openGalleryViewer(index)}>
            {isImage ? (
              <Image
                source={{ uri: item.path || item.uri }}
                style={styles.mediaItem}
              />
            ) : (
              <View style={styles.videoContainer}>
                <Video
                  source={{ uri: item.path || item.uri }}
                  style={styles.mediaItem}
                  paused={true}
                  resizeMode="cover"
                  muted
                />
                <View style={styles.playOverlay}>
                  <Icon name="play-circle-outline" size={30} color="#fff" />
                </View>
              </View>
            )}
          </TouchableOpacity>
        );
      }

      if (isAudio) {
        const audioUri = item.uri || item.path;
        const isThisPlaying = isPlaying && currentPlayingUri === audioUri;

        return (
          <View key={index} style={styles.audioBubble}>
            <TouchableOpacity onPress={() => onPlay(audioUri)}>
              <Icon
                name={isThisPlaying ? 'pause-circle' : 'play-circle'}
                size={35}
                color="#fff"
              />
            </TouchableOpacity>
            <WaveForm
              source={{ uri: audioUri }}
              play={isThisPlaying}
              style={{ height: 25, width: '60%' }}
              waveFormStyle={{ waveColor: Color.gray, scrubColor: '#fff' }}
            />
            <Text style={{ marginLeft: 10, color: Color.gray }}>
              {item?.duration || duration}
            </Text>
          </View>
        );
      }

      return null;
    };

    return (
      <View style={styles.mediaContainer}>
        {message?.media?.length <= 4 ? (
          message.media.map((item, index) => renderItem(item, index))
        ) : (
          <>
            {message.media.slice(0, 3).map((item, index) => renderItem(item, index))}
            <View style={styles.moreOverlay}>
              {renderItem(message.media[3], 3)}
              <View style={styles.moreCountOverlay}>
                <Text style={styles.moreCountText}>+{message.media.length - 3}</Text>
              </View>
            </View>
          </>
        )}
      </View>
    );
  };

  return (
    <>
    <TouchableOpacity onLongPress={handleLongPress}>
      <View
        style={[
          styles.container,
          isSender ? styles.alignRight : styles.alignLeft,
        ]}
        >
        <View style={[styles.bubble, isSender ? styles.sender : styles.receiver]}>
          {message.replyTo && (
            <View style={styles.replyContainer}>
              <View style={styles.replyLine} />
              <View style={styles.replyContent}>
                <Text style={styles.replySender}>
                  {message.replyTo.senderName}
                </Text>
                <Text style={styles.replyText} numberOfLines={1}>
                  {message.replyTo.text || <Image source={{ uri: message.replyTo?.media[0]?.uri }} style={{ width: 20, height: 20 }} />}
                </Text>
              </View>
            </View>
          )}

          {renderMedia()}

          {editingMessage && editingMessage.id === message.id ? (
            <View style={styles.editContainer}>
              <TextInput
                style={styles.editInput}
                value={editText}
                onChangeText={onEditTextChange}
                multiline
                autoFocus
              />
              <View style={styles.editButtons}>
                <TouchableOpacity onPress={onCancelEdit} style={styles.editButton}>
                  <Icon name="close" size={16} color="#666" />
                </TouchableOpacity>
                <TouchableOpacity onPress={onSaveEdit} style={styles.editButton}>
                  <Icon name="check" size={16} color="#25D366" />
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <>
              {message.text ? (
                <View>
                  <Text style={styles.messageText}>
                    {message.deleted ? 'This message was deleted' : message.text}
                  </Text>
                  {message.edited && !message.deleted && (
                    <Text style={styles.editedLabel}>edited</Text>
                  )}
                </View>
              ) : null}
            </>
          )}

          <View style={styles.timeRow}>
            <Text style={styles.timeText}>{message.time}</Text>
            {isSender && <Icon name="check-all" size={16} color="#34B7F1" />}
          </View>
        </View>
      </View>
            </TouchableOpacity>

      <MediaGalleryModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        mediaList={message.media}
        initialIndex={selectedMediaIndex}
      />

    </>
  );
};

const styles = StyleSheet.create({
  container: { marginVertical: 4, marginHorizontal: 10 },
  alignRight: { alignItems: 'flex-end' },
  alignLeft: { alignItems: 'flex-start' },
  bubble: {
    borderRadius: 12,
    padding: 8,
    maxWidth: '80%',
  },
  sender: { backgroundColor: '#005C4B' },
  receiver: { backgroundColor: '#262626' },
  messageText: { color: '#fff', fontSize: 16, marginTop: 6 },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  timeText: { fontSize: 12, color: '#ccc', marginRight: 4 },
  mediaContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  mediaItem: {
    width: 100,
    height: 100,
    borderRadius: 8,
    margin: 2,
  },
  videoContainer: {
    position: 'relative',
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#00000033',
    borderRadius: 8,
  },
  moreOverlay: {
    position: 'relative',
  },
  moreCountOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000000aa',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  moreCountText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  audioBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    marginTop: 6,
  },
  // Reply preview styles
  replyContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 8,
  },
  replyLine: {
    width: 3,
    backgroundColor: '#25D366',
    borderRadius: 2,
    marginRight: 8,
  },
  replyContent: {
    flex: 1,
  },
  replySender: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#25D366',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 13,
    color: '#ccc',
    fontStyle: 'italic',
  },
  // Edit functionality styles
  editContainer: {
    marginTop: 6,
  },
  editInput: {
    backgroundColor: '#333',
    color: '#fff',
    borderRadius: 8,
    padding: 8,
    fontSize: 16,
    minHeight: 40,
  },
  editButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
    gap: 12,
  },
  editButton: {
    padding: 4,
  },
  editedLabel: {
    fontSize: 11,
    color: '#888',
    fontStyle: 'italic',
    marginTop: 2,
  },
});

export default ChatMessage;