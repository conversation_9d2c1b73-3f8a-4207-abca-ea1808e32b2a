import React, { useState } from 'react';
import {
  View, Text, TextInput, Image, TouchableOpacity,
  StyleSheet, ScrollView, Switch, ActivityIndicator, Modal, Pressable, Alert
} from 'react-native';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import MentionDropdown from '../../component/MentionDropDown';
import Mention from '../../component/Mention';
import MentionModal from '../../component/Mention';
import MoreOptionsModal from '../../component/MoreOptionModal';
import { useDispatch } from 'react-redux';
import { createPost } from '../../redux/action/PostAction';
import CustomToast from '../../component/CustomToast';

const Upload = ({ route, navigation }) => {
  const dispatch = useDispatch();
  const { media } = route.params || {};

  const [caption, setCaption] = useState('');
  const [selection, setSelection] = useState({ start: 0, end: 0 });
  const [isPrivate, setIsPrivate] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [mentionModal, setMentionModal] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [music, setMusic] = useState('');
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const renderMedia = () => {
    if (media.mime?.startsWith('image/')) {
      return <Image source={{ uri: media.path }} style={styles.previewImage} />;
    } else if (media.mime?.startsWith('video/')) {
      return (
        <Video
          source={{ uri: media.path }}
          style={styles.previewImage}
          resizeMode="cover"
          repeat
        />
      );
    }
    return null;
  };

  const handlePost = async () => {
    if (!media) {
      showToast('error', 'Please select a media file');
      return;
    }

    if (!caption.trim()) {
      showToast('error', 'Please add a caption');
      return;
    }

    setUploading(true);
    setProgress(0);

    try {
      const postData = {
        description: caption,
        music: music || 'Original Sound',
        privacy: isPrivate ? 'only_friends' : 'everyone'
      };

      if (media.mime?.startsWith('video/')) {
        postData.video = {
          uri: media.path,
          type: media.mime,
          name: `video_${Date.now()}.mp4`
        };
      } else if (media.mime?.startsWith('image/')) {
        postData.image = {
          uri: media.path,
          type: media.mime,
          name: `image_${Date.now()}.jpg`
        };
      }

      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await createPost(postData)(dispatch);

      clearInterval(progressInterval);
      setProgress(100);

      if (result.success) {
        showToast('success', 'Post uploaded successfully!');
        setTimeout(() => {
          setUploading(false);
          navigation.goBack();
        }, 1500);
      } else {
        setUploading(false);
        showToast('error', result.error || 'Failed to upload post');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setUploading(false);
      showToast('error', 'Upload failed. Please try again.');
    }
  };

  const insertAtCursor = (textToInsert) => {
    const { start, end } = selection;
    const newText = caption.substring(0, start) + textToInsert + caption.substring(end);
    const cursorPos = start + textToInsert.length;
    setCaption(newText);
    setSelection({ start: cursorPos, end: cursorPos });
  };

  return (
    <>
      <View style={styles.container}>
        <ScrollView style={styles.scroll}>
          <TextInput
            style={styles.input}
            placeholder="Add description..."
            placeholderTextColor='gray'
            multiline
            value={caption}
            onChangeText={setCaption}
            onSelectionChange={({ nativeEvent: { selection } }) => setSelection(selection)}
            selection={selection}
          />

          <TextInput
            style={[styles.input, { marginTop: 10 }]}
            placeholder="Add music/sound (optional)..."
            placeholderTextColor='gray'
            value={music}
            onChangeText={setMusic}
          />

          <View style={styles.row}>
            <TouchableOpacity style={styles.tagBtn} onPress={() => insertAtCursor('#')}>
              <Text style={styles.tagText}># Hashtags</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.tagBtn} onPress={() => insertAtCursor('') || setMentionModal(true)}>
              <Text style={styles.tagText}>@ Mention</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.mediaWrapper}>
            <View style={styles.mediaContainer}>
              {renderMedia()}
              <TouchableOpacity style={styles.editCover}>
                <Text style={styles.editText}>Edit cover</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity style={styles.optionRow}>
            <Icon name="link-outline" size={20} color="#000" />
            <Text style={styles.optionText}>Add link</Text>
          </TouchableOpacity>

          <View style={styles.optionRow}>
            <Icon name={isPrivate ? "lock-closed-outline" : "earth-outline"} size={20} color={isPrivate ? "red" : "green"} />
            <Text style={styles.optionText}>{isPrivate ? 'Only friends can view' : 'Everyone can view'}</Text>
            <Switch
              value={isPrivate}
              onValueChange={setIsPrivate}
              trackColor={{ false: '#ccc', true: Color.Main }}
            />
          </View>

          <TouchableOpacity style={styles.optionRow} onPress={() => setShowMoreOptions(true)}>
            <Icon name="ellipsis-horizontal-outline" size={20} color="#000" />
            <Text style={styles.optionText}>More options</Text>
          </TouchableOpacity>
        </ScrollView>

        {uploading && (
          <View style={styles.progressContainer}>
            <ActivityIndicator size="small" color="#FF0050" />
            <Text style={styles.progressText}>{progress}% uploading...</Text>
          </View>
        )}

        <View style={styles.footer}>
          <TouchableOpacity style={styles.draftBtn}>
            <Text style={styles.draftText}>Drafts</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.postBtn} onPress={handlePost} disabled={uploading}>
            <Text style={styles.postText}>{uploading ? 'Posting...' : 'Post'}</Text>
          </TouchableOpacity>
        </View>
      </View>
      <MentionModal visible={mentionModal}
       onClose={() => setMentionModal(false)}
        onSelectMention={(mention) => insertAtCursor(mention)} />
      <MoreOptionsModal visible={showMoreOptions} onClose={() => setShowMoreOptions(false)} />

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

export default Upload;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  scroll: { padding: 16 },
  input: {
    borderBottomWidth: 1,
    borderColor: '#ccc',
    fontSize: 16,
    marginBottom: 12,
    paddingBottom: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 20,
  },
  tagBtn: {
    backgroundColor: '#eee',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  tagText: {
    color: Color.Primary,
    fontFamily: fontFamilies.POPPINS.bold
  },
  mediaWrapper: {
    marginBottom: 24,
  },
  mediaContainer: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#000',
    position: 'relative',
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  editCover: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    backgroundColor: 'rgba(0,0,0,0.4)',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  editText: {
    color: '#fff',
    fontSize: 13,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderColor: '#eee',
    gap: 10,
  },
  optionText: {
    fontSize: 15,
    flex: 1,
    color: Color.Primary,
    fontFamily: fontFamilies.POPPINS.medium
  },
  footer: {
    flexDirection: 'row',
    padding: 12,
    borderTopWidth: 1,
    borderColor: '#eee',
    backgroundColor: '#fff',
  },
  draftBtn: {
    flex: 1,
    backgroundColor: '#eee',
    padding: 14,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  postBtn: {
    flex: 1,
    backgroundColor: '#FF0050',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  draftText: {
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Primary,
  },
  postText: {
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderTopWidth: 1,
    borderColor: '#eee',
    backgroundColor: '#fff',
  },
  progressText: {
    marginLeft: 10,
    color: '#FF0050',
    fontWeight: '600',
  },

  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContent: {
    backgroundColor: '#fff',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 12,
  },
  modalMentionTitle: {
    fontSize: 17,
    fontFamily: fontFamilies.POPPINS.extraBold,
  },
  mentionInput: {
    backgroundColor: 'darkgray',
    borderRadius: 20,
    padding: 10
  },
  modalItem: {
    paddingVertical: 12,
  },
  modalText: {
    fontSize: 15,
    color: '#000',
  },
});
