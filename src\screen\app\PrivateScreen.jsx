import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  SafeAreaView,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-community/blur';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';

const OPTIONS = [
  { id: 'only_you', label: 'Only You', icon: 'lock-closed-outline' },
  { id: 'only_friends', label: 'Only Friends', icon: 'people-outline' },
  { id: 'everyone', label: 'Everyone', icon: 'earth-outline' },
];

const PrivateScreen = () => {
  const [selected, setSelected] = useState('only_you');
  const navigation = useNavigation();

  return (
    <LinearGradient colors={['#333399', '#ff00cc']} style={styles.container}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Privacy Settings</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.overlay}>
          {Platform.OS === 'ios' && (
            <BlurView style={styles.card} blurType="light" blurAmount={20} />
          )}
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.card}
          >
            {OPTIONS.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.option,
                  selected === item.id && styles.optionSelected,
                ]}
                onPress={() => setSelected(item.id)}
              >
                <Icon name={item.icon} size={22} color="#fff" style={styles.icon} />
                <Text style={styles.optionText}>{item.label}</Text>
                <View style={[styles.circle, selected === item.id && styles.circleSelected]}>
                  {selected === item.id && <View style={styles.innerCircle} />}
                </View>
              </TouchableOpacity>
            ))}
          </LinearGradient>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

export default PrivateScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginTop: '12%',
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.extraBold,
    color: '#fff',
    textAlign: 'center',
  },
  overlay: {
    marginTop: 30,
    marginHorizontal: 20,
    overflow: 'hidden',
  },
  card: {
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  optionSelected: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderRadius: 10,
    padding: 8,
  },
  icon: {
    marginRight: 12,
  },
  optionText: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.bold,
  },
  circle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  circleSelected: {
    borderColor: Color.Primary,
  },
  innerCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Color.Primary,
  },
});
