const express = require('express');
const http = require('http');
// const socketIo = require('socket.io'); // TODO: Install socket.io
const multer = require('multer');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Complete TikTok Server...');

const app = express();
const server = http.createServer(app);
// TODO: Uncomment when socket.io is installed
// const io = socketIo(server, {
//   cors: {
//     origin: "*",
//     methods: ["GET", "POST"]
//   }
// });

// Create upload directories
const uploadDirs = ['uploads/posts', 'uploads/profiles', 'uploads/videos'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Manual CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }
  next();
});

// JSON parser middleware
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    if (file.fieldname === 'profileImage') {
      cb(null, 'uploads/profiles/');
    } else if (file.fieldname === 'video') {
      cb(null, 'uploads/videos/');
    } else {
      cb(null, 'uploads/posts/');
    }
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

console.log('✅ Middleware configured');

// TODO: Socket.IO connection handling (uncomment when socket.io is installed)
const connectedUsers = new Map(); // Store connected users

// io.on('connection', (socket) => {
//   console.log('👤 User connected:', socket.id);

//   // User joins with their ID
//   socket.on('join', (userId) => {
//     connectedUsers.set(userId, socket.id);
//     socket.userId = userId;
//     console.log(`👤 User ${userId} joined with socket ${socket.id}`);
//   });

//   // Handle sending messages
//   socket.on('sendMessage', (data) => {
//     const { receiverId, message } = data;
//     const receiverSocketId = connectedUsers.get(receiverId);

//     if (receiverSocketId) {
//       io.to(receiverSocketId).emit('newMessage', message);
//       console.log(`💬 Message sent from ${socket.userId} to ${receiverId}`);
//     }
//   });

//   // Handle typing indicators
//   socket.on('typing', (data) => {
//     const { receiverId, isTyping } = data;
//     const receiverSocketId = connectedUsers.get(receiverId);

//     if (receiverSocketId) {
//       io.to(receiverSocketId).emit('userTyping', {
//         userId: socket.userId,
//         isTyping
//       });
//     }
//   });

//   // Handle user disconnect
//   socket.on('disconnect', () => {
//     if (socket.userId) {
//       connectedUsers.delete(socket.userId);
//       console.log(`👤 User ${socket.userId} disconnected`);
//     }
//   });
// });

console.log('✅ Server configured (Socket.IO ready for installation)');

// Mock Database
let users = [];
let posts = [];
let comments = [];
let likes = [];
let follows = [];
let messages = [];
let notifications = [];

// Helper functions
const generateId = () => Date.now().toString() + Math.random().toString(36).substr(2, 9);
const findUser = (id) => users.find(u => u.id === id);
const findPost = (id) => posts.find(p => p.id === id);

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root route accessed');
  res.json({ 
    message: 'Complete TikTok Server is working!',
    timestamp: new Date().toISOString(),
    status: 'OK',
    endpoints: {
      auth: '/api/auth/*',
      posts: '/api/posts/*',
      users: '/api/users/*',
      chat: '/api/chat/*',
      notifications: '/api/notifications/*'
    }
  });
});

// ==================== AUTH APIS ====================

// Register API
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register API called:', req.body);
  
  const { firstName, lastName, email, password, phone, address } = req.body;
  
  if (!firstName || !lastName || !email || !password || !phone || !address) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  // Check if user already exists
  const existingUser = users.find(u => u.email === email);
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User already exists with this email'
    });
  }

  const newUser = {
    id: generateId(),
    firstName,
    lastName,
    name: `${firstName} ${lastName}`,
    email,
    password, // In real app, hash this
    phone,
    address,
    bio: '',
    profileImage: null,
    isVerified: false,
    isProfileComplete: false,
    followersCount: 0,
    followingCount: 0,
    postsCount: 0,
    createdAt: new Date().toISOString()
  };

  users.push(newUser);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: 'token-' + newUser.id,
    user: newUser
  });
});

// Login API
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login API called:', req.body);
  
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  const user = users.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  res.json({
    success: true,
    message: 'Login successful',
    token: 'token-' + user.id,
    user: user
  });
});

// Complete Profile API
app.post('/api/auth/complete-profile', (req, res) => {
  console.log('👤 Complete Profile API called:', req.body);
  
  const { bio, gender, dateOfBirth, interests, profileImage } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');
  
  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Update user profile
  user.bio = bio || '';
  user.gender = gender || '';
  user.dateOfBirth = dateOfBirth || '';
  user.interests = interests || [];
  user.profileImage = profileImage || null;
  user.isProfileComplete = true;

  res.json({
    success: true,
    message: 'Profile completed successfully',
    user: user
  });
});

// Update Profile API
app.put('/api/auth/profile', (req, res) => {
  console.log('📝 Update Profile API called:', req.body);
  
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');
  
  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const { firstName, lastName, bio, phone, address } = req.body;
  
  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (firstName || lastName) user.name = `${user.firstName} ${user.lastName}`;
  if (bio) user.bio = bio;
  if (phone) user.phone = phone;
  if (address) user.address = address;

  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: user
  });
});

// Change Password API
app.post('/api/auth/change-password', (req, res) => {
  console.log('🔑 Change Password API called:', req.body);
  
  const { currentPassword, newPassword } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');
  
  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  if (user.password !== currentPassword) {
    return res.status(400).json({
      success: false,
      message: 'Current password is incorrect'
    });
  }

  user.password = newPassword;

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

// Logout API
app.post('/api/auth/logout', (req, res) => {
  console.log('🚪 Logout API called');
  
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Forgot Password API
app.post('/api/auth/forgot-password', (req, res) => {
  console.log('🔑 Forgot Password API called:', req.body);
  
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  const otp = '123456';
  
  res.json({
    success: true,
    message: 'OTP sent to your email',
    otp: otp
  });
});

// Verify OTP API
app.post('/api/auth/verify-otp', (req, res) => {
  console.log('🔢 Verify OTP API called:', req.body);
  
  const { email, otp } = req.body;
  
  if (!email || !otp) {
    return res.status(400).json({
      success: false,
      message: 'Email and OTP are required'
    });
  }

  if (otp === '123456') {
    res.json({
      success: true,
      message: 'OTP verified successfully'
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Invalid OTP'
    });
  }
});

// Reset Password API
app.post('/api/auth/reset-password', (req, res) => {
  console.log('🔐 Reset Password API called:', req.body);
  
  const { email, otp, newPassword } = req.body;
  
  if (!email || !otp || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Email, OTP and new password are required'
    });
  }

  const user = users.find(u => u.email === email);
  if (user) {
    user.password = newPassword;
  }

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

// ==================== POSTS APIS ====================

// Get All Posts (Feed)
app.get('/api/posts', (req, res) => {
  console.log('📱 Get Posts API called');

  const postsWithUserData = posts.map(post => {
    const user = findUser(post.userId);
    const postLikes = likes.filter(l => l.postId === post.id);
    const postComments = comments.filter(c => c.postId === post.id);

    return {
      ...post,
      user: {
        id: user?.id,
        name: user?.name,
        profileImage: user?.profileImage
      },
      likesCount: postLikes.length,
      commentsCount: postComments.length,
      isLiked: false // TODO: Check if current user liked
    };
  });

  res.json({
    success: true,
    posts: postsWithUserData
  });
});

// Create Post
app.post('/api/posts', upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'image', maxCount: 1 }
]), (req, res) => {
  console.log('📝 Create Post API called:', req.body);
  console.log('Files:', req.files);

  const { description, music, privacy } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const newPost = {
    id: generateId(),
    userId: userId,
    description: description || '',
    music: music || '',
    privacy: privacy || 'everyone',
    video: req.files?.video?.[0]?.filename || null,
    image: req.files?.image?.[0]?.filename || null,
    likesCount: 0,
    commentsCount: 0,
    sharesCount: 0,
    createdAt: new Date().toISOString()
  };

  posts.push(newPost);
  user.postsCount += 1;

  res.status(201).json({
    success: true,
    message: 'Post created successfully',
    post: newPost
  });
});

// Like/Unlike Post
app.post('/api/posts/:postId/like', (req, res) => {
  console.log('❤️ Like Post API called:', req.params.postId);

  const { postId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const post = findPost(postId);
  if (!post) {
    return res.status(404).json({
      success: false,
      message: 'Post not found'
    });
  }

  const existingLike = likes.find(l => l.postId === postId && l.userId === userId);

  if (existingLike) {
    // Unlike
    likes = likes.filter(l => l.id !== existingLike.id);
    res.json({
      success: true,
      message: 'Post unliked',
      isLiked: false
    });
  } else {
    // Like
    const newLike = {
      id: generateId(),
      postId,
      userId,
      createdAt: new Date().toISOString()
    };
    likes.push(newLike);

    // Create notification
    if (post.userId !== userId) {
      notifications.push({
        id: generateId(),
        userId: post.userId,
        fromUserId: userId,
        type: 'like',
        postId: postId,
        message: `${findUser(userId)?.name} liked your post`,
        isRead: false,
        createdAt: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      message: 'Post liked',
      isLiked: true
    });
  }
});

// Add Comment
app.post('/api/posts/:postId/comments', (req, res) => {
  console.log('💬 Add Comment API called:', req.params.postId, req.body);

  const { postId } = req.params;
  const { text } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const post = findPost(postId);
  if (!post) {
    return res.status(404).json({
      success: false,
      message: 'Post not found'
    });
  }

  const newComment = {
    id: generateId(),
    postId,
    userId,
    text,
    createdAt: new Date().toISOString()
  };

  comments.push(newComment);

  // Create notification
  if (post.userId !== userId) {
    notifications.push({
      id: generateId(),
      userId: post.userId,
      fromUserId: userId,
      type: 'comment',
      postId: postId,
      message: `${findUser(userId)?.name} commented on your post`,
      isRead: false,
      createdAt: new Date().toISOString()
    });
  }

  res.status(201).json({
    success: true,
    message: 'Comment added successfully',
    comment: {
      ...newComment,
      user: {
        id: findUser(userId)?.id,
        name: findUser(userId)?.name,
        profileImage: findUser(userId)?.profileImage
      }
    }
  });
});

// Get Post Comments
app.get('/api/posts/:postId/comments', (req, res) => {
  console.log('💬 Get Comments API called:', req.params.postId);

  const { postId } = req.params;
  const postComments = comments.filter(c => c.postId === postId);

  const commentsWithUserData = postComments.map(comment => ({
    ...comment,
    user: {
      id: findUser(comment.userId)?.id,
      name: findUser(comment.userId)?.name,
      profileImage: findUser(comment.userId)?.profileImage
    }
  }));

  res.json({
    success: true,
    comments: commentsWithUserData
  });
});

// ==================== USERS APIS ====================

// Search Users
app.get('/api/users/search', (req, res) => {
  console.log('🔍 Search Users API called:', req.query);

  const { q } = req.query;

  if (!q) {
    return res.json({
      success: true,
      users: []
    });
  }

  const searchResults = users.filter(user =>
    user.name.toLowerCase().includes(q.toLowerCase()) ||
    user.email.toLowerCase().includes(q.toLowerCase())
  ).map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    profileImage: user.profileImage,
    bio: user.bio,
    followersCount: user.followersCount,
    isFollowing: false // TODO: Check if current user follows this user
  }));

  res.json({
    success: true,
    users: searchResults
  });
});

// Get User Profile
app.get('/api/users/:userId', (req, res) => {
  console.log('👤 Get User Profile API called:', req.params.userId);

  const { userId } = req.params;
  const user = findUser(userId);

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const userPosts = posts.filter(p => p.userId === userId);

  res.json({
    success: true,
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      bio: user.bio,
      followersCount: user.followersCount,
      followingCount: user.followingCount,
      postsCount: user.postsCount,
      isFollowing: false // TODO: Check if current user follows this user
    },
    posts: userPosts
  });
});

// Follow/Unfollow User
app.post('/api/users/:userId/follow', (req, res) => {
  console.log('👥 Follow User API called:', req.params.userId);

  const { userId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const userToFollow = findUser(userId);
  const currentUser = findUser(currentUserId);

  if (!userToFollow || !currentUser) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const existingFollow = follows.find(f => f.followerId === currentUserId && f.followingId === userId);

  if (existingFollow) {
    // Unfollow
    follows = follows.filter(f => f.id !== existingFollow.id);
    userToFollow.followersCount -= 1;
    currentUser.followingCount -= 1;

    res.json({
      success: true,
      message: 'User unfollowed',
      isFollowing: false
    });
  } else {
    // Follow
    const newFollow = {
      id: generateId(),
      followerId: currentUserId,
      followingId: userId,
      createdAt: new Date().toISOString()
    };
    follows.push(newFollow);
    userToFollow.followersCount += 1;
    currentUser.followingCount += 1;

    // Create notification
    notifications.push({
      id: generateId(),
      userId: userId,
      fromUserId: currentUserId,
      type: 'follow',
      message: `${currentUser.name} started following you`,
      isRead: false,
      createdAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'User followed',
      isFollowing: true
    });
  }
});

// Get Followers
app.get('/api/users/:userId/followers', (req, res) => {
  console.log('👥 Get Followers API called:', req.params.userId);

  const { userId } = req.params;
  const userFollowers = follows.filter(f => f.followingId === userId);

  const followersWithData = userFollowers.map(follow => {
    const follower = findUser(follow.followerId);
    return {
      id: follower?.id,
      name: follower?.name,
      profileImage: follower?.profileImage,
      bio: follower?.bio,
      followersCount: follower?.followersCount
    };
  });

  res.json({
    success: true,
    followers: followersWithData
  });
});

// Get Following
app.get('/api/users/:userId/following', (req, res) => {
  console.log('👥 Get Following API called:', req.params.userId);

  const { userId } = req.params;
  const userFollowing = follows.filter(f => f.followerId === userId);

  const followingWithData = userFollowing.map(follow => {
    const following = findUser(follow.followingId);
    return {
      id: following?.id,
      name: following?.name,
      profileImage: following?.profileImage,
      bio: following?.bio,
      followersCount: following?.followersCount
    };
  });

  res.json({
    success: true,
    following: followingWithData
  });
});

// ==================== CHAT APIS ====================

// Get Chat List (Inbox)
app.get('/api/chat', (req, res) => {
  console.log('💬 Get Chat List API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  // Get unique conversations for the user
  const userMessages = messages.filter(m => m.senderId === userId || m.receiverId === userId);
  const conversations = {};

  userMessages.forEach(message => {
    const otherUserId = message.senderId === userId ? message.receiverId : message.senderId;
    if (!conversations[otherUserId] || new Date(message.createdAt) > new Date(conversations[otherUserId].lastMessage.createdAt)) {
      conversations[otherUserId] = {
        userId: otherUserId,
        user: findUser(otherUserId),
        lastMessage: message,
        unreadCount: 0 // TODO: Calculate unread count
      };
    }
  });

  const chatList = Object.values(conversations).map(conv => ({
    userId: conv.userId,
    user: {
      id: conv.user?.id,
      name: conv.user?.name,
      profileImage: conv.user?.profileImage
    },
    lastMessage: {
      text: conv.lastMessage.text,
      createdAt: conv.lastMessage.createdAt,
      isFromMe: conv.lastMessage.senderId === userId
    },
    unreadCount: conv.unreadCount
  }));

  res.json({
    success: true,
    chats: chatList
  });
});

// Get Messages with User
app.get('/api/chat/:userId', (req, res) => {
  console.log('💬 Get Messages API called:', req.params.userId);

  const { userId: otherUserId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const conversation = messages.filter(m =>
    (m.senderId === currentUserId && m.receiverId === otherUserId) ||
    (m.senderId === otherUserId && m.receiverId === currentUserId)
  ).sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

  res.json({
    success: true,
    messages: conversation,
    user: findUser(otherUserId)
  });
});

// Send Message
app.post('/api/chat/:userId', (req, res) => {
  console.log('💬 Send Message API called:', req.params.userId, req.body);

  const { userId: receiverId } = req.params;
  const { text } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const senderId = token?.replace('token-', '');

  const newMessage = {
    id: generateId(),
    senderId,
    receiverId,
    text,
    isRead: false,
    createdAt: new Date().toISOString()
  };

  messages.push(newMessage);

  // Create notification
  notifications.push({
    id: generateId(),
    userId: receiverId,
    fromUserId: senderId,
    type: 'message',
    message: `${findUser(senderId)?.name} sent you a message`,
    isRead: false,
    createdAt: new Date().toISOString()
  });

  // TODO: Emit Socket.IO event for real-time messaging (uncomment when socket.io is installed)
  // const receiverSocketId = connectedUsers.get(receiverId);
  // if (receiverSocketId) {
  //   io.to(receiverSocketId).emit('newMessage', newMessage);
  //   console.log(`💬 Real-time message sent to user ${receiverId}`);
  // }

  res.status(201).json({
    success: true,
    message: 'Message sent successfully',
    data: newMessage
  });
});

// ==================== NOTIFICATIONS APIS ====================

// Get Notifications
app.get('/api/notifications', (req, res) => {
  console.log('🔔 Get Notifications API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const userNotifications = notifications
    .filter(n => n.userId === userId)
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .map(notification => ({
      ...notification,
      fromUser: findUser(notification.fromUserId),
      post: notification.postId ? findPost(notification.postId) : null
    }));

  res.json({
    success: true,
    notifications: userNotifications
  });
});

// Mark Notification as Read
app.put('/api/notifications/:notificationId/read', (req, res) => {
  console.log('🔔 Mark Notification Read API called:', req.params.notificationId);

  const { notificationId } = req.params;
  const notification = notifications.find(n => n.id === notificationId);

  if (notification) {
    notification.isRead = true;
  }

  res.json({
    success: true,
    message: 'Notification marked as read'
  });
});

// Mark All Notifications as Read
app.put('/api/notifications/read-all', (req, res) => {
  console.log('🔔 Mark All Notifications Read API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  notifications.forEach(notification => {
    if (notification.userId === userId) {
      notification.isRead = true;
    }
  });

  res.json({
    success: true,
    message: 'All notifications marked as read'
  });
});

// ==================== UPLOAD APIS ====================

// Upload Profile Image
app.post('/api/upload/profile', upload.single('profileImage'), (req, res) => {
  console.log('📷 Upload Profile Image API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  user.profileImage = req.file.filename;

  res.json({
    success: true,
    message: 'Profile image uploaded successfully',
    profileImage: req.file.filename,
    url: `/uploads/profiles/${req.file.filename}`
  });
});

const PORT = 3001;

app.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Server failed to start:', err);
    return;
  }
  console.log(`🚀 Complete TikTok Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`📍 Network: http://***************:${PORT}`);
  console.log('✅ Server started successfully!');
});

// ==================== AUTH APIS ====================

// Register API
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register API called:', req.body);

  const { firstName, lastName, email, password, phone, address } = req.body;

  if (!firstName || !lastName || !email || !password || !phone || !address) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  // Check if user already exists
  const existingUser = users.find(u => u.email === email);
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'User already exists with this email'
    });
  }

  const newUser = {
    id: generateId(),
    firstName,
    lastName,
    name: `${firstName} ${lastName}`,
    email,
    password, // In real app, hash this
    phone,
    address,
    bio: '',
    profileImage: null,
    isVerified: false,
    isProfileComplete: false,
    followersCount: 0,
    followingCount: 0,
    postsCount: 0,
    createdAt: new Date().toISOString()
  };

  users.push(newUser);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: 'token-' + newUser.id,
    user: newUser
  });
});

// Login API
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login API called:', req.body);

  const { email, password } = req.body;

  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  const user = users.find(u => u.email === email && u.password === password);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  res.json({
    success: true,
    message: 'Login successful',
    token: 'token-' + user.id,
    user: user
  });
});

// Complete Profile API
app.post('/api/auth/complete-profile', (req, res) => {
  console.log('👤 Complete Profile API called:', req.body);

  const { bio, gender, dateOfBirth, interests, profileImage } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Update user profile
  user.bio = bio || '';
  user.gender = gender || '';
  user.dateOfBirth = dateOfBirth || '';
  user.interests = interests || [];
  user.profileImage = profileImage || null;
  user.isProfileComplete = true;

  res.json({
    success: true,
    message: 'Profile completed successfully',
    user: user
  });
});

// Update Profile API
app.put('/api/auth/profile', (req, res) => {
  console.log('📝 Update Profile API called:', req.body);

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const { firstName, lastName, bio, phone, address } = req.body;

  if (firstName) user.firstName = firstName;
  if (lastName) user.lastName = lastName;
  if (firstName || lastName) user.name = `${user.firstName} ${user.lastName}`;
  if (bio) user.bio = bio;
  if (phone) user.phone = phone;
  if (address) user.address = address;

  res.json({
    success: true,
    message: 'Profile updated successfully',
    user: user
  });
});

// Change Password API
app.post('/api/auth/change-password', (req, res) => {
  console.log('🔑 Change Password API called:', req.body);

  const { currentPassword, newPassword } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  if (user.password !== currentPassword) {
    return res.status(400).json({
      success: false,
      message: 'Current password is incorrect'
    });
  }

  user.password = newPassword;

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

// Forgot Password API
app.post('/api/auth/forgot-password', (req, res) => {
  console.log('🔑 Forgot Password API called:', req.body);

  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  const otp = '123456';

  res.json({
    success: true,
    message: 'OTP sent to your email',
    otp: otp
  });
});

// Verify OTP API
app.post('/api/auth/verify-otp', (req, res) => {
  console.log('🔢 Verify OTP API called:', req.body);

  const { email, otp } = req.body;

  if (!email || !otp) {
    return res.status(400).json({
      success: false,
      message: 'Email and OTP are required'
    });
  }

  if (otp === '123456') {
    res.json({
      success: true,
      message: 'OTP verified successfully'
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Invalid OTP'
    });
  }
});

// Reset Password API
app.post('/api/auth/reset-password', (req, res) => {
  console.log('🔐 Reset Password API called:', req.body);

  const { email, otp, newPassword } = req.body;

  if (!email || !otp || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Email, OTP and new password are required'
    });
  }

  const user = users.find(u => u.email === email);
  if (user) {
    user.password = newPassword;
  }

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

// Logout API
app.post('/api/auth/logout', (req, res) => {
  console.log('🚪 Logout API called');

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// ==================== POSTS APIS ====================

// Get All Posts (Feed)
app.get('/api/posts', (req, res) => {
  console.log('📱 Get Posts API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const postsWithUserData = posts.map(post => {
    const user = findUser(post.userId);
    const postLikes = likes.filter(l => l.postId === post.id);
    const postComments = comments.filter(c => c.postId === post.id);
    const isLiked = postLikes.some(l => l.userId === currentUserId);

    return {
      ...post,
      user: {
        id: user?.id,
        name: user?.name,
        profileImage: user?.profileImage,
        isVerified: user?.isVerified
      },
      likesCount: postLikes.length,
      commentsCount: postComments.length,
      isLiked: isLiked,
      videoUrl: post.video ? `/uploads/videos/${post.video}` : null,
      imageUrl: post.image ? `/uploads/images/${post.image}` : null
    };
  }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  res.json({
    success: true,
    posts: postsWithUserData
  });
});

// Get Following Posts (Friend Feed)
app.get('/api/posts/following', (req, res) => {
  console.log('👥 Get Following Posts API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  // Get users that current user follows
  const following = follows.filter(f => f.followerId === currentUserId);
  const followingIds = following.map(f => f.followingId);

  // Get posts from followed users
  const followingPosts = posts.filter(post => followingIds.includes(post.userId));

  const postsWithUserData = followingPosts.map(post => {
    const user = findUser(post.userId);
    const postLikes = likes.filter(l => l.postId === post.id);
    const postComments = comments.filter(c => c.postId === post.id);
    const isLiked = postLikes.some(l => l.userId === currentUserId);

    return {
      ...post,
      user: {
        id: user?.id,
        name: user?.name,
        profileImage: user?.profileImage,
        isVerified: user?.isVerified
      },
      likesCount: postLikes.length,
      commentsCount: postComments.length,
      isLiked: isLiked,
      videoUrl: post.video ? `/uploads/videos/${post.video}` : null,
      imageUrl: post.image ? `/uploads/images/${post.image}` : null
    };
  }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  res.json({
    success: true,
    posts: postsWithUserData
  });
});

// Create Post
app.post('/api/posts', upload.fields([
  { name: 'video', maxCount: 1 },
  { name: 'image', maxCount: 1 }
]), (req, res) => {
  console.log('📝 Create Post API called:', req.body);
  console.log('Files:', req.files);

  const { description, music, privacy } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const user = findUser(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const newPost = {
    id: generateId(),
    userId: userId,
    description: description || '',
    music: music || '',
    privacy: privacy || 'everyone',
    video: req.files?.video?.[0]?.filename || null,
    image: req.files?.image?.[0]?.filename || null,
    likesCount: 0,
    commentsCount: 0,
    sharesCount: 0,
    viewsCount: 0,
    createdAt: new Date().toISOString()
  };

  posts.push(newPost);
  user.postsCount += 1;

  res.status(201).json({
    success: true,
    message: 'Post created successfully',
    post: {
      ...newPost,
      user: {
        id: user.id,
        name: user.name,
        profileImage: user.profileImage,
        isVerified: user.isVerified
      },
      videoUrl: newPost.video ? `/uploads/videos/${newPost.video}` : null,
      imageUrl: newPost.image ? `/uploads/images/${newPost.image}` : null
    }
  });
});

// Like/Unlike Post
app.post('/api/posts/:postId/like', (req, res) => {
  console.log('❤️ Like Post API called:', req.params.postId);

  const { postId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const post = findPost(postId);
  if (!post) {
    return res.status(404).json({
      success: false,
      message: 'Post not found'
    });
  }

  const existingLike = likes.find(l => l.postId === postId && l.userId === userId);

  if (existingLike) {
    // Unlike
    likes = likes.filter(l => l.id !== existingLike.id);
    post.likesCount = Math.max(0, post.likesCount - 1);

    res.json({
      success: true,
      message: 'Post unliked',
      isLiked: false,
      likesCount: post.likesCount
    });
  } else {
    // Like
    const newLike = {
      id: generateId(),
      postId,
      userId,
      createdAt: new Date().toISOString()
    };
    likes.push(newLike);
    post.likesCount += 1;

    // Create notification
    if (post.userId !== userId) {
      notifications.push({
        id: generateId(),
        userId: post.userId,
        fromUserId: userId,
        type: 'like',
        postId: postId,
        message: `${findUser(userId)?.name} liked your post`,
        isRead: false,
        createdAt: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      message: 'Post liked',
      isLiked: true,
      likesCount: post.likesCount
    });
  }
});

// Add Comment
app.post('/api/posts/:postId/comments', (req, res) => {
  console.log('💬 Add Comment API called:', req.params.postId, req.body);

  const { postId } = req.params;
  const { text } = req.body;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const post = findPost(postId);
  if (!post) {
    return res.status(404).json({
      success: false,
      message: 'Post not found'
    });
  }

  const newComment = {
    id: generateId(),
    postId,
    userId,
    text,
    likesCount: 0,
    createdAt: new Date().toISOString()
  };

  comments.push(newComment);
  post.commentsCount += 1;

  // Create notification
  if (post.userId !== userId) {
    notifications.push({
      id: generateId(),
      userId: post.userId,
      fromUserId: userId,
      type: 'comment',
      postId: postId,
      message: `${findUser(userId)?.name} commented on your post`,
      isRead: false,
      createdAt: new Date().toISOString()
    });
  }

  res.status(201).json({
    success: true,
    message: 'Comment added successfully',
    comment: {
      ...newComment,
      user: {
        id: findUser(userId)?.id,
        name: findUser(userId)?.name,
        profileImage: findUser(userId)?.profileImage
      }
    }
  });
});

// Get Post Comments
app.get('/api/posts/:postId/comments', (req, res) => {
  console.log('💬 Get Comments API called:', req.params.postId);

  const { postId } = req.params;
  const postComments = comments.filter(c => c.postId === postId);

  const commentsWithUserData = postComments.map(comment => ({
    ...comment,
    user: {
      id: findUser(comment.userId)?.id,
      name: findUser(comment.userId)?.name,
      profileImage: findUser(comment.userId)?.profileImage
    }
  })).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  res.json({
    success: true,
    comments: commentsWithUserData
  });
});

// Get Single Post
app.get('/api/posts/:postId', (req, res) => {
  console.log('📱 Get Single Post API called:', req.params.postId);

  const { postId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const post = findPost(postId);
  if (!post) {
    return res.status(404).json({
      success: false,
      message: 'Post not found'
    });
  }

  const user = findUser(post.userId);
  const postLikes = likes.filter(l => l.postId === post.id);
  const postComments = comments.filter(c => c.postId === post.id);
  const isLiked = postLikes.some(l => l.userId === currentUserId);

  const postWithData = {
    ...post,
    user: {
      id: user?.id,
      name: user?.name,
      profileImage: user?.profileImage,
      isVerified: user?.isVerified
    },
    likesCount: postLikes.length,
    commentsCount: postComments.length,
    isLiked: isLiked,
    videoUrl: post.video ? `/uploads/videos/${post.video}` : null,
    imageUrl: post.image ? `/uploads/images/${post.image}` : null
  };

  res.json({
    success: true,
    post: postWithData
  });
});

// ==================== USERS APIS ====================

// Search Users
app.get('/api/users/search', (req, res) => {
  console.log('🔍 Search Users API called:', req.query);

  const { q } = req.query;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  if (!q) {
    return res.json({
      success: true,
      users: []
    });
  }

  const searchResults = users.filter(user =>
    user.name.toLowerCase().includes(q.toLowerCase()) ||
    user.email.toLowerCase().includes(q.toLowerCase())
  ).map(user => {
    const isFollowing = follows.some(f => f.followerId === currentUserId && f.followingId === user.id);

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      bio: user.bio,
      followersCount: user.followersCount,
      isVerified: user.isVerified,
      isFollowing: isFollowing
    };
  });

  res.json({
    success: true,
    users: searchResults
  });
});

// Get User Profile
app.get('/api/users/:userId', (req, res) => {
  console.log('👤 Get User Profile API called:', req.params.userId);

  const { userId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const user = findUser(userId);

  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const userPosts = posts.filter(p => p.userId === userId).map(post => {
    const postLikes = likes.filter(l => l.postId === post.id);
    const postComments = comments.filter(c => c.postId === post.id);
    const isLiked = postLikes.some(l => l.userId === currentUserId);

    return {
      ...post,
      likesCount: postLikes.length,
      commentsCount: postComments.length,
      isLiked: isLiked,
      videoUrl: post.video ? `/uploads/videos/${post.video}` : null,
      imageUrl: post.image ? `/uploads/images/${post.image}` : null
    };
  }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

  const isFollowing = follows.some(f => f.followerId === currentUserId && f.followingId === userId);

  res.json({
    success: true,
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      profileImage: user.profileImage,
      bio: user.bio,
      followersCount: user.followersCount,
      followingCount: user.followingCount,
      postsCount: user.postsCount,
      isVerified: user.isVerified,
      isFollowing: isFollowing
    },
    posts: userPosts
  });
});

// Follow/Unfollow User
app.post('/api/users/:userId/follow', (req, res) => {
  console.log('👥 Follow User API called:', req.params.userId);

  const { userId } = req.params;
  const token = req.headers.authorization?.replace('Bearer ', '');
  const currentUserId = token?.replace('token-', '');

  const userToFollow = findUser(userId);
  const currentUser = findUser(currentUserId);

  if (!userToFollow || !currentUser) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  const existingFollow = follows.find(f => f.followerId === currentUserId && f.followingId === userId);

  if (existingFollow) {
    // Unfollow
    follows = follows.filter(f => f.id !== existingFollow.id);
    userToFollow.followersCount -= 1;
    currentUser.followingCount -= 1;

    res.json({
      success: true,
      message: 'User unfollowed',
      isFollowing: false
    });
  } else {
    // Follow
    const newFollow = {
      id: generateId(),
      followerId: currentUserId,
      followingId: userId,
      createdAt: new Date().toISOString()
    };
    follows.push(newFollow);
    userToFollow.followersCount += 1;
    currentUser.followingCount += 1;

    // Create notification
    notifications.push({
      id: generateId(),
      userId: userId,
      fromUserId: currentUserId,
      type: 'follow',
      message: `${currentUser.name} started following you`,
      isRead: false,
      createdAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'User followed',
      isFollowing: true
    });
  }
});

// Get Notifications
app.get('/api/notifications', (req, res) => {
  console.log('🔔 Get Notifications API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  const userNotifications = notifications
    .filter(n => n.userId === userId)
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .map(notification => ({
      ...notification,
      fromUser: findUser(notification.fromUserId),
      post: notification.postId ? findPost(notification.postId) : null
    }));

  res.json({
    success: true,
    notifications: userNotifications
  });
});

// Get Chat List (Inbox)
app.get('/api/chat', (req, res) => {
  console.log('💬 Get Chat List API called');

  const token = req.headers.authorization?.replace('Bearer ', '');
  const userId = token?.replace('token-', '');

  // Get unique conversations for the user
  const userMessages = messages.filter(m => m.senderId === userId || m.receiverId === userId);
  const conversations = {};

  userMessages.forEach(message => {
    const otherUserId = message.senderId === userId ? message.receiverId : message.senderId;
    if (!conversations[otherUserId] || new Date(message.createdAt) > new Date(conversations[otherUserId].lastMessage.createdAt)) {
      conversations[otherUserId] = {
        userId: otherUserId,
        user: findUser(otherUserId),
        lastMessage: message,
        unreadCount: 0
      };
    }
  });

  const chatList = Object.values(conversations).map(conv => ({
    userId: conv.userId,
    user: {
      id: conv.user?.id,
      name: conv.user?.name,
      profileImage: conv.user?.profileImage
    },
    lastMessage: {
      text: conv.lastMessage.text,
      createdAt: conv.lastMessage.createdAt,
      isFromMe: conv.lastMessage.senderId === userId
    },
    unreadCount: conv.unreadCount
  }));

  res.json({
    success: true,
    chats: chatList
  });
});

console.log('');
// Server is already started above with app.listen()
// All endpoints are configured and ready
