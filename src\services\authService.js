// Auth Service for React Native App
import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'http://localhost:3001/api/auth';

class AuthService {
  // Helper method to get auth token
  async getToken() {
    try {
      return await AsyncStorage.getItem('authToken');
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  // Helper method to save auth token
  async saveToken(token) {
    try {
      await AsyncStorage.setItem('authToken', token);
    } catch (error) {
      console.error('Error saving token:', error);
    }
  }

  // Helper method to remove auth token
  async removeToken() {
    try {
      await AsyncStorage.removeItem('authToken');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  // Helper method to make API calls
  async apiCall(endpoint, method = 'GET', data = null, requiresAuth = false) {
    try {
      const config = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (requiresAuth) {
        const token = await this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      }

      if (data) {
        config.body = JSON.stringify(data);
      }

      const response = await fetch(`${BASE_URL}${endpoint}`, config);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'API call failed');
      }

      return result;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // 1. Register User
  async register(userData) {
    try {
      const result = await this.apiCall('/register', 'POST', userData);
      if (result.success && result.token) {
        await this.saveToken(result.token);
      }
      return result;
    } catch (error) {
      throw error;
    }
  }

  // 2. Login User
  async login(email, password) {
    try {
      const result = await this.apiCall('/login', 'POST', { email, password });
      if (result.success && result.token) {
        await this.saveToken(result.token);
      }
      return result;
    } catch (error) {
      throw error;
    }
  }

  // 3. Forgot Password
  async forgotPassword(email) {
    try {
      return await this.apiCall('/forgot-password', 'POST', { email });
    } catch (error) {
      throw error;
    }
  }

  // 4. Verify OTP
  async verifyOTP(email, otp) {
    try {
      return await this.apiCall('/verify-otp', 'POST', { email, otp });
    } catch (error) {
      throw error;
    }
  }

  // 5. Reset Password
  async resetPassword(email, otp, newPassword) {
    try {
      return await this.apiCall('/reset-password', 'POST', { email, otp, newPassword });
    } catch (error) {
      throw error;
    }
  }

  // 6. Get User Profile
  async getProfile() {
    try {
      return await this.apiCall('/profile', 'GET', null, true);
    } catch (error) {
      throw error;
    }
  }

  // 7. Update Profile
  async updateProfile(profileData) {
    try {
      return await this.apiCall('/profile', 'PUT', profileData, true);
    } catch (error) {
      throw error;
    }
  }

  // 8. Change Password
  async changePassword(currentPassword, newPassword) {
    try {
      return await this.apiCall('/change-password', 'POST', { currentPassword, newPassword }, true);
    } catch (error) {
      throw error;
    }
  }

  // 9. Upload Profile Image
  async uploadProfileImage(imageUri) {
    try {
      const token = await this.getToken();
      if (!token) {
        throw new Error('No auth token found');
      }

      const formData = new FormData();
      formData.append('profileImage', {
        uri: imageUri,
        type: 'image/jpeg',
        name: 'profile.jpg',
      });

      const response = await fetch(`${BASE_URL}/upload-profile-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.message || 'Upload failed');
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  // 10. Logout
  async logout() {
    try {
      await this.apiCall('/logout', 'POST', null, true);
      await this.removeToken();
      return { success: true, message: 'Logged out successfully' };
    } catch (error) {
      // Even if API call fails, remove local token
      await this.removeToken();
      throw error;
    }
  }

  // 11. Verify Token
  async verifyToken() {
    try {
      return await this.apiCall('/verify-token', 'GET', null, true);
    } catch (error) {
      // If token verification fails, remove it
      await this.removeToken();
      throw error;
    }
  }

  // 12. Check if user is logged in
  async isLoggedIn() {
    try {
      const token = await this.getToken();
      if (!token) return false;

      const result = await this.verifyToken();
      return result.success;
    } catch (error) {
      return false;
    }
  }

  // 13. Get current user data
  async getCurrentUser() {
    try {
      const result = await this.getProfile();
      return result.success ? result.user : null;
    } catch (error) {
      return null;
    }
  }
}

export default new AuthService();
