import React, { useState } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Text,
} from 'react-native';

import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomToast from './CustomToast';

const { width, height } = Dimensions.get('window');

const MediaGalleryModal = ({ visible, onClose, mediaList, initialIndex = 0 }) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [toastData, setToastData] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleMediaError = (type) => {
    setToastData({ type: 'error', message: `Failed to load ${type}` });
  };

  const renderItem = ({ item }) => {
    const isImage = item.type === 'image';
    const isVideo = item.type === 'video';

    if (isImage) {
      return (
        <Image
          source={{ uri: item.uri || item.path }}
          style={styles.media}
          resizeMode="contain"
          onError={() => handleMediaError('image')}
          PlaceholderContent={<ActivityIndicator />}
        />
      );
    }

    if (isVideo) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          {loading && <ActivityIndicator size="large" color="#fff" style={{ position: 'absolute', top: height/2 - 40 }} />}
          <Video
            source={{ uri: item.uri || item.path }}
            style={styles.media}
            resizeMode="contain"
            controls
            paused={false}
            onError={() => handleMediaError('video')}
            onLoadStart={() => setLoading(true)}
            onLoad={() => setLoading(false)}
          />
        </View>
      );
    }

    return <Text style={{ color: '#fff', textAlign: 'center' }}>Unsupported media</Text>;
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Icon name="close" size={30} color="#fff" />
        </TouchableOpacity>

        <FlatList
          horizontal
          pagingEnabled
          data={mediaList}
          renderItem={renderItem}
          keyExtractor={(_, index) => index.toString()}
          initialScrollIndex={initialIndex}
          onMomentumScrollEnd={(e) => {
            const index = Math.round(
              e.nativeEvent.contentOffset.x / width
            );
            setCurrentIndex(index);
          }}
        />
        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={() => setToastData(null)}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#000',
  },
  media: {
    width,
    height,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
});

export default MediaGalleryModal;
