import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { fontFamilies } from '../constant/Font';
import { Color } from '../constant/color';
import { useNavigation } from '@react-navigation/native';

const ChatHeader = ({ name, image, onBackPress, onWallpaperPress }) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(-10)).current;
  const navigation = useNavigation()

  useEffect(() => {
    if (dropdownVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: -10,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [dropdownVisible]);

  const handleOption = (option) => {
    setDropdownVisible(false);
    switch (option) {
      case 'viewProfile':
        console.log('View Profile Pressed');
        break;
      case 'wallpaper':
        onWallpaperPress?.();
        break;
      case 'clearChat':
        console.log('Clear Chat Pressed');
        break;
    }
  };

  return (
    <View>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBackPress}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>

{/* <TouchableOpacity onPress={() => navigation.navigate('ViewInboxProfile')}> */}
        <Image source={image} style={styles.avatar} />

        <TouchableOpacity onPress={() => navigation.navigate('ViewInboxProfile')} style={styles.headerText}>
          <Text style={styles.name}>{name}</Text>
          <Text style={styles.status}>Online</Text>
        </TouchableOpacity>
        {/* </TouchableOpacity> */}

        <TouchableOpacity onPress={() => setDropdownVisible((prev) => !prev)}>
          <Icon name="ellipsis-vertical" size={24} color="#fff" style={styles.icon} />
        </TouchableOpacity>
      </View>

      {dropdownVisible && (
        <TouchableWithoutFeedback onPress={() => setDropdownVisible(false)}>
          <View style={styles.dropdownOverlay}>
            <Animated.View
              style={[
                styles.dropdown,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY }],
                },
              ]}
            >
              <TouchableOpacity onPress={() => handleOption('viewProfile')} style={styles.dropdownItem}>
                <Text style={styles.dropdownText}>View Profile</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => handleOption('wallpaper')} style={styles.dropdownItem}>
                <Text style={styles.dropdownText}>Change Wallpaper</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => handleOption('clearChat')} style={styles.dropdownItem}>
                <Text style={styles.dropdownText}>Clear Chat</Text>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

export default ChatHeader;

const styles = StyleSheet.create({
  header: {
    height: 60,
    backgroundColor: Color.Main,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    zIndex: 2,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginHorizontal: 10,
  },
  headerText: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.medium,
  },
  status: {
    color: '#ccc',
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.light,
  },
  icon: {
    marginLeft: 15,
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 60,
    right: 10,
    zIndex: 3,
  },
  dropdown: {
    backgroundColor: Color.inputBg,
    borderRadius: 10,
    paddingVertical: 5,
    paddingHorizontal: 10,
    width: 180,
    elevation: 10,
  },
  dropdownItem: {
    paddingVertical: 10,
  },
  dropdownText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.regular,
  },
});
