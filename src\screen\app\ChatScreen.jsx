import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  SafeAreaView,
  FlatList,
  StatusBar,
  ImageBackground,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Alert,
  ToastAndroid,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import ImagePicker from 'react-native-image-crop-picker';

import ChatHeader from '../../component/ChatHeader';
import ChatInput from '../../component/ChatInput';
import ChatMessage from '../../component/ChatMessage';
import { Color } from '../../constant/color';
import ActionSheetModal from '../../component/ActionSheetModal';
import { useDispatch, useSelector } from 'react-redux';
import { getMessages, sendMessage } from '../../redux/action/ChatAction';
import CustomToast from '../../component/CustomToast';
// import io from 'socket.io-client';

const audioRecorder = new AudioRecorderPlayer();

const ChatScreen = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const { messages: reduxMessages, loading } = useSelector(state => state.chat || {});
  const { user: currentUser } = useSelector(state => state.auth || {});

  const { name, image, userId, userName, userImage } = route.params;
  const chatUserId = userId || 'default-user-id';
  const chatUserName = userName || name || 'Unknown User';
  const chatUserImage = userImage || image;

  const [messages, setMessages] = useState([]);
  const [text, setText] = useState('');
  const [media, setMedia] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recPath, setRecPath] = useState('');
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recDuration, setRecDuration] = useState('0:00');
  const [actionVisible, setActionVisible] = useState(false);
  const [wallpaper, setWallpaper] = useState(null);
  const [toastData, setToastData] = useState(null);

  // New state for message interactions
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [replyingTo, setReplyingTo] = useState(null);
  const [editingMessage, setEditingMessage] = useState(null);
  const [editText, setEditText] = useState('');

  // Socket.IO ref for real-time messaging
  // const socketRef = useRef(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  // Load messages on component mount
  useEffect(() => {
    if (chatUserId) {
      loadMessages();
    }
  }, [chatUserId]);

  // Update local messages when Redux messages change
  useEffect(() => {
    if (reduxMessages && reduxMessages.length > 0) {
      // Convert Redux messages to local format
      const formattedMessages = reduxMessages.map(msg => ({
        id: msg.id,
        text: msg.text,
        time: new Date(msg.createdAt).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        sent: msg.senderId === currentUser?.id,
        delivered: true,
        seen: msg.isRead || false,
        type: 'text'
      }));
      setMessages(formattedMessages.reverse());
    }
  }, [reduxMessages, currentUser?.id]);

  const loadMessages = async () => {
    try {
      const result = await getMessages(chatUserId)(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load messages');
      }
    } catch (error) {
      console.error('Load messages error:', error);
      showToast('error', 'Failed to load messages');
    }
  };

  const updateTime = (e) => {
    const m = Math.floor(e.currentPosition / 1000 / 60);
    const s = Math.floor((e.currentPosition / 1000) % 60).toString().padStart(2, '0');
    setRecDuration(`${m}:${s}`);
  };

  const onStartRecording = async () => {
    setIsRecording(true);
    setIsPaused(false);
    const path = await audioRecorder.startRecorder();
    setRecPath(path);
    audioRecorder.addRecordBackListener(updateTime);
  };

  const onStopRecording = async () => {
    await audioRecorder.stopRecorder();
    audioRecorder.removeRecordBackListener();
    setIsRecording(false);
    setIsPreviewing(true);
  };

  const onPlayPreview = async () => {
    await audioRecorder.startPlayer(recPath);
    setIsPaused(false);
  };

  const onPausePreview = async () => {
    await audioRecorder.pausePlayer();
    setIsPaused(true);
  };

  const onCancelPreview = async () => {
    await audioRecorder.stopPlayer();
    setIsPreviewing(false);
    setRecPath('');
    setRecDuration('0:00');
  };

  const onImagePick = async () => {
    try {
      const result = await ImagePicker.openPicker({
        multiple: true,
        mediaType: 'any',
      });
      setMedia((prev) => [...prev, ...result]);
    } catch (e) {
    }
  };

  const onRemoveMedia = (index) => {
    setMedia((prev) => prev.filter((_, i) => i !== index));
  };

  const onSend = async () => {
    const hasText = text.trim().length > 0;
    const hasMedia = media.length > 0;
    const isAudioReady = isPreviewing && recPath;

    if (!hasText && !hasMedia && !isAudioReady) return;

    const messageText = text.trim();
    setText(''); // Clear input immediately
    setMedia([]);
    setReplyingTo(null);

    try {
      // Send message via API
      const result = await sendMessage(chatUserId, messageText)(dispatch);

      if (result.success) {
        // Message will be added via Redux state update
        showToast('success', 'Message sent');

        // Emit socket event for real-time messaging
        // socketRef.current?.emit('sendMessage', {
        //   receiverId: chatUserId,
        //   message: result.message
        // });
      } else {
        showToast('error', result.error || 'Failed to send message');
        setText(messageText); // Restore message on failure
      }
    } catch (error) {
      console.error('Send message error:', error);
      showToast('error', 'Failed to send message');
      setText(messageText); // Restore message on failure
    }

    // Legacy code for media and audio (keep for now)
    const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    let newMessage = {
      id: Date.now().toString(),
      sent: true,
      time,
      text: '',
      media: [],
      replyTo: replyingTo ? {
        id: replyingTo.id,
        text: replyingTo.text,
        sent: replyingTo.sent,
        senderName: replyingTo.sent ? 'You' : name,
      } : null,
    };

    if (isAudioReady) {
      newMessage.media.push({ uri: recPath, type: 'audio', duration: recDuration });
      await audioRecorder.stopPlayer();
      setIsPreviewing(false);
      setRecPath('');
      setRecDuration('0:00');
    }

    if (hasMedia) {
      newMessage.media.push(
        ...media.map((item) => ({
          uri: item.path,
          type: item.mime.includes('video') ? 'video' : 'image',
        }))
      );
      setMedia([]);
    }

    if (hasText) {
      newMessage.text = text.trim();
      setText('');
    }

    setMessages((prev) => [newMessage, ...prev]);

    // Clear reply state after sending
    if (replyingTo) {
      setReplyingTo(null);
    }
  };

  const handleWallpaperPick = async () => {
    try {
      const result = await ImagePicker.openPicker({
        mediaType: 'photo',
        cropping: true,
      });
      setWallpaper({ uri: result.path });
    } catch (e) {
      console.log('Wallpaper selection cancelled');
    }
  };

  const handleMessageLongPress = (message) => {
    setSelectedMessage(message);
    setActionVisible(true);
  };

  const handleReply = () => {
    setReplyingTo(selectedMessage);
    setActionVisible(false);
    setSelectedMessage(null);
  };

  const handleEdit = () => {
    if (selectedMessage && selectedMessage.sender) {
      setEditingMessage(selectedMessage);
      setEditText(selectedMessage.text || '');
      setActionVisible(false);
      setSelectedMessage(null);
    }
  };

  const handleCopy = () => {
    if (selectedMessage && selectedMessage.text) {
      Clipboard.setString(selectedMessage.text);
      if (Platform.OS === 'android') {
        ToastAndroid.show('Message copied', ToastAndroid.SHORT);
      }
    }
    setActionVisible(false);
    setSelectedMessage(null);
  };

  const handleDelete = () => {
    if (!selectedMessage) return;

    const isOwnMessage = selectedMessage.sender;

    if (isOwnMessage) {
      Alert.alert(
        'Delete Message',
        'Choose delete option',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete for me',
            onPress: () => deleteMessage(selectedMessage.id, 'me'),
          },
          {
            text: 'Delete for everyone',
            onPress: () => deleteMessage(selectedMessage.id, 'everyone'),
            style: 'destructive',
          },
        ]
      );
    } else {
      Alert.alert(
        'Delete Message',
        'Delete this message for you?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete for me',
            onPress: () => deleteMessage(selectedMessage.id, 'me'),
            style: 'destructive',
          },
        ]
      );
    }

    setActionVisible(false);
    setSelectedMessage(null);
  };

  const deleteMessage = (messageId, deleteType) => {
    setMessages(prevMessages => {
      return prevMessages.map(msg => {
        if (msg.id === messageId) {
          if (deleteType === 'everyone') {
            return {
              ...msg,
              text: 'This message was deleted',
              media: [],
              deleted: true,
              deletedForEveryone: true,
            };
          } else {
            // For "delete for me", we remove it from the local state
            return null;
          }
        }
        return msg;
      }).filter(Boolean);
    });
  };

  const cancelReply = () => {
    setReplyingTo(null);
  };

  const cancelEdit = () => {
    setEditingMessage(null);
    setEditText('');
  };

  const saveEdit = () => {
    if (editingMessage && editText.trim()) {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === editingMessage.id
            ? { ...msg, text: editText.trim(), edited: true }
            : msg
        )
      );
      setEditingMessage(null);
      setEditText('');
    }
  };

  return (
    <>
      <SafeAreaView style={styles.container}>
        <StatusBar backgroundColor={Color.Main} barStyle="light-content" />
        <ChatHeader
          name={name}
          image={image}
          onBackPress={() => navigation.goBack()}
          onWallpaperPress={handleWallpaperPick}
        />

        <ImageBackground
          style={styles.chatArea}
          source={wallpaper }
          resizeMode="cover"
        >
          <FlatList
            data={messages}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <ChatMessage
                message={item}
                isSender={item.sender}
                onLongPress={() => handleMessageLongPress(item)}
                editingMessage={editingMessage}
                editText={editText}
                onEditTextChange={setEditText}
                onSaveEdit={saveEdit}
                onCancelEdit={cancelEdit}
              />
            )}
            inverted
            contentContainerStyle={{ padding: 10 }}
          />

            </ImageBackground>
          <KeyboardAvoidingView behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
            <ChatInput
              value={text}
              onChangeText={setText}
              onSend={onSend}
              onImagePress={onImagePick}
              selectedMedia={media}
              onRemoveMedia={onRemoveMedia}
              isRecording={isRecording}
              recordingDuration={recDuration}
              onStartRecording={onStartRecording}
              onStopRecording={onStopRecording}
              isPaused={isPaused}
              onPlayPreview={onPlayPreview}
              onPausePreview={onPausePreview}
              isPreviewing={isPreviewing}
              onCancelPreview={onCancelPreview}
              recordedFilePath={recPath}
              finalRecordTime={recDuration}
              isPlaying={!isPaused && isPreviewing}
              replyingTo={replyingTo}
              onCancelReply={cancelReply}
            />
          </KeyboardAvoidingView>

          {toastData && (
            <CustomToast
              type={toastData.type}
              message={toastData.message}
              onHide={hideToast}
            />
          )}
      </SafeAreaView>

      <ActionSheetModal
        visible={actionVisible}
        onClose={() => setActionVisible(false)}
        onEdit={handleEdit}
        onReply={handleReply}
        onDelete={handleDelete}
        onCopy={selectedMessage ? handleCopy : () => {
          const allText = messages.map((item) => item?.text).join('\n');
          Clipboard.setString(allText);
          setActionVisible(false);
        }}
        selectedMessage={selectedMessage}
        onWallpaperPick={handleWallpaperPick}
        onClearChat={() => {
          setMessages([]);
          setActionVisible(false);
        }}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0D0D1F',
  },
  chatArea: {
    flex: 1,
  },
});

export default ChatScreen;
