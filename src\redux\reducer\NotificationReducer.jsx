import {
  SET_LOADING,
  SET_ERROR,
  CLEAR_ERROR,
  SET_NOTIFICATIONS,
  ADD_NOTIFICATION,
  MARK_NOTIFICATION_READ,
  CLEAR_ALL_NOTIFICATIONS
} from '../action/NotificationAction';

const initialState = {
  notifications: [],
  loading: false,
  error: null,
  unreadCount: 0
};

const NotificationReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case SET_NOTIFICATIONS:
      const unreadCount = action.payload.filter(notification => !notification.isRead).length;
      return {
        ...state,
        notifications: action.payload,
        unreadCount: unreadCount,
        loading: false
      };

    case ADD_NOTIFICATION:
      const newNotifications = [action.payload, ...state.notifications];
      const newUnreadCount = newNotifications.filter(notification => !notification.isRead).length;
      return {
        ...state,
        notifications: newNotifications,
        unreadCount: newUnreadCount
      };

    case MARK_NOTIFICATION_READ:
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === action.payload
          ? { ...notification, isRead: true }
          : notification
      );
      const updatedUnreadCount = updatedNotifications.filter(notification => !notification.isRead).length;
      return {
        ...state,
        notifications: updatedNotifications,
        unreadCount: updatedUnreadCount
      };

    case CLEAR_ALL_NOTIFICATIONS:
      return {
        ...state,
        notifications: [],
        unreadCount: 0
      };

    default:
      return state;
  }
};

export default NotificationReducer;
