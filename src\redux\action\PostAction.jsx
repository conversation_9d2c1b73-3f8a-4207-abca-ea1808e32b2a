import AsyncStorage from '@react-native-async-storage/async-storage';

// API Base URL
const BASE_URL = 'http://192.168.100.150:3002/api';

// Action Types
export const SET_LOADING = 'SET_LOADING';
export const SET_ERROR = 'SET_ERROR';
export const CLEAR_ERROR = 'CLEAR_ERROR';
export const SET_POSTS = 'SET_POSTS';
export const ADD_POST = 'ADD_POST';
export const UPDATE_POST = 'UPDATE_POST';
export const SET_COMMENTS = 'SET_COMMENTS';
export const ADD_COMMENT = 'ADD_COMMENT';

// Action Creators
export const setLoading = (loading) => ({
  type: SET_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: SET_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

export const setPosts = (posts) => ({
  type: SET_POSTS,
  payload: posts
});

export const addPost = (post) => ({
  type: ADD_POST,
  payload: post
});

export const updatePost = (post) => ({
  type: UPDATE_POST,
  payload: post
});

export const setComments = (comments) => ({
  type: SET_COMMENTS,
  payload: comments
});

export const addComment = (comment) => ({
  type: ADD_COMMENT,
  payload: comment
});

// Get All Posts (Feed)
export const getPosts = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for getPosts:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get posts API response:', result);

      if (result.success) {
        dispatch(setPosts(result.posts));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, posts: result.posts };
    } catch (error) {
      console.error('Get posts error:', error);
      dispatch(setError(error.message || 'Failed to get posts'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Following Posts (Friend Feed)
export const getFollowingPosts = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for getFollowingPosts:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts/following`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get following posts API response:', result);

      if (result.success) {
        dispatch(setPosts(result.posts));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, posts: result.posts };
    } catch (error) {
      console.error('Get following posts error:', error);
      dispatch(setError(error.message || 'Failed to get following posts'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Create Post
export const createPost = (postData) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for createPost:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      // Validate media URIs to prevent empty source errors
      if (postData.video && (!postData.video.uri || postData.video.uri.trim() === '')) {
        console.warn('Empty video URI detected, removing video from post');
        delete postData.video;
      }
      
      if (postData.image && (!postData.image.uri || postData.image.uri.trim() === '')) {
        console.warn('Empty image URI detected, removing image from post');
        delete postData.image;
      }

      const formData = new FormData();
      formData.append('description', postData.description || '');
      formData.append('music', postData.music || '');
      formData.append('privacy', postData.privacy || 'everyone');

      if (postData.video && postData.video.uri) {
        console.log('Adding video to post:', postData.video.uri);
        formData.append('video', {
          uri: postData.video.uri,
          type: postData.video.type || 'video/mp4',
          name: postData.video.name || 'video.mp4'
        });
      }

      if (postData.image && postData.image.uri) {
        console.log('Adding image to post:', postData.image.uri);
        formData.append('image', {
          uri: postData.image.uri,
          type: postData.image.type || 'image/jpeg',
          name: postData.image.name || 'image.jpg'
        });
      }

      const response = await fetch(`${BASE_URL}/posts`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        body: formData
      });

      const result = await response.json();
      console.log('Create post API response:', result);

      if (result.success) {
        dispatch(addPost(result.post));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, post: result.post };
    } catch (error) {
      console.error('Create post error:', error);
      dispatch(setError(error.message || 'Failed to create post'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Like/Unlike Post
export const likePost = (postId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for likePost:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts/${postId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Like post API response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Like post error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Add Comment
export const addCommentToPost = (postId, text) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for addCommentToPost:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts/${postId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ text })
      });

      const result = await response.json();
      console.log('Add comment API response:', result);

      if (result.success) {
        dispatch(addComment(result.comment));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, comment: result.comment };
    } catch (error) {
      console.error('Add comment error:', error);
      dispatch(setError(error.message || 'Failed to add comment'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Post Comments
export const getPostComments = (postId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts/${postId}/comments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get comments API response:', result);

      if (result.success) {
        dispatch(setComments(result.comments));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, comments: result.comments };
    } catch (error) {
      console.error('Get comments error:', error);
      dispatch(setError(error.message || 'Failed to get comments'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Single Post
export const getSinglePost = (postId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/posts/${postId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get single post API response:', result);

      dispatch(setLoading(false));
      return { success: result.success, data: result, post: result.post };
    } catch (error) {
      console.error('Get single post error:', error);
      dispatch(setError(error.message || 'Failed to get post'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};
