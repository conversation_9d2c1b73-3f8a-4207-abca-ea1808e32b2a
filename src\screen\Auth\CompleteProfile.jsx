import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import ImagePicker from 'react-native-image-crop-picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import CustomInput from '../../component/CustomInput';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { completeProfile } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';
import AsyncStorage from '@react-native-async-storage/async-storage';

const CompleteProfile = ({navigation}) => {
  const dispatch = useDispatch();
  const {user, loading } = useSelector(state => state.auth || {});

  const [image, setImage] = useState(null);
  const [username, setUsername] = useState('');
  const [bio, setBio] = useState('');
  const [gender, setGender] = useState('');
  const [dob, setDob] = useState(new Date());
  const [showDate, setShowDate] = useState(false);
  const [interests, setInterests] = useState('');
  const [errors, setErrors] = useState({});
  const [toastData, setToastData] = useState(null);

  console.log('user=============================>', user)

  useEffect(() => {
    const loadUserData = async () => {
      try {
        // First try Redux state
        if (user) {
          console.log('Loading user from Redux:', user);
          setUsername(user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim());
          return;
        }

        const storedUser = await AsyncStorage.getItem('userData');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          console.log('Loading user from AsyncStorage:', userData);
          setUsername(userData.name || `${userData.firstName || ''} ${userData.lastName || ''}`.trim());
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    loadUserData();
  }, [user]);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const selectImage = async () => {
    try {
      const img = await ImagePicker.openPicker({
        width: 300,
        height: 300,
        cropping: true,
        cropperCircleOverlay: true,
        compressImageQuality: 0.8,
      });
      setImage(img.path);
    } catch (error) {
      console.log('Image pick cancelled', error);
    }
  };

  const validateFields = () => {
    const newErrors = {};
    if (!bio.trim()) newErrors.bio = 'Bio is required';
    if (!gender) newErrors.gender = 'Please select a gender';
    if (!dob) newErrors.dob = 'Date of birth is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateFields()) {
      showToast('error', 'Please fill all required fields');
      return;
    }

    try {
      const profileData = {
        bio: bio.trim(),
        gender,
        dateOfBirth: dob.toISOString(),
        interests: interests.split(',').map(item => item.trim()).filter(item => item),
        profileImage: image || null
      };

      console.log('Completing profile with data:', profileData);

      const result = await completeProfile(profileData)(dispatch);

      console.log('Complete profile result:', result);

      if (result.success) {
        showToast('success', 'Profile completed successfully!');

        // Wait a moment for Redux state to update, then navigate
        setTimeout(() => {
          try {
            // Try multiple navigation methods
            const parentNav = navigation.getParent();
            if (parentNav) {
              console.log('Using parent navigation to reset to MainTabs');
              parentNav.reset({
                index: 0,
                routes: [{ name: 'MainTabs' }],
              });
            } else {
              console.log('No parent navigation found, trying direct navigation');
              navigation.navigate('MainTabs');
            }
          } catch (error) {
            console.error('Navigation error:', error);
            // Fallback: The AppNavigator should automatically detect the state change
            console.log('Relying on AppNavigator automatic detection');
          }
        }, 1500);
      } else {
        showToast('error', result.message || 'Failed to complete profile');
      }
    } catch (error) {
      console.error('Complete profile error:', error);
      showToast('error', 'Failed to complete profile. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.background}>
      <LinearGradient colors={['#333399', '#ff00cc']} style={styles.background}>
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.overlay}>
            {Platform.OS === 'ios' && (
              <BlurView style={styles.glass} blurType="light" blurAmount={20} />
            )}
            <LinearGradient
              colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
              style={styles.glass}
            >
              <Text style={styles.title}>Complete Your Profile</Text>

              <TouchableOpacity onPress={selectImage} style={styles.imagePicker}>
                {image && image.trim() !== '' ? (
                  <Image source={{ uri: image }} style={styles.image} />
                ) : (
                  <Image
                    source={require('../../assets/image/profileImage.jpg')}
                    style={styles.image}
                  />
                )}
              </TouchableOpacity>

              <CustomInput
                label="Username"
                value={username}
                onChangeText={setUsername}
                placeholder="Your display name"
                iconLeft="person-outline"
                editable={false}
                style={{ backgroundColor: 'rgba(255,255,255,0.05)' }}
              />

              <CustomInput
                label="Short Bio"
                value={bio}
                onChangeText={setBio}
                placeholder="Tell us about yourself..."
                iconLeft="document-text-outline"
                multiline
                numberOfLines={4}
                style={{ height: 80 }}
                error={errors.bio}
              />

              <CustomInput
                label="Interests"
                value={interests}
                onChangeText={setInterests}
                placeholder="e.g. Music, Sports, Travel (comma separated)"
                iconLeft="heart-outline"
              />

              <View style={styles.pickerWrapper}>
                <Picker
                  selectedValue={gender}
                  onValueChange={setGender}
                  style={styles.picker}
                >
                  <Picker.Item label="Select Gender" value="" />
                  <Picker.Item label="Male" value="male" />
                  <Picker.Item label="Female" value="female" />
                  <Picker.Item label="Other" value="other" />
                </Picker>
              </View>
              {errors.gender && <Text style={styles.error}>{errors.gender}</Text>}

              <TouchableOpacity onPress={() => setShowDate(true)} style={styles.dateBtn}>
                <Text style={styles.dateText}>
                  {dob ? dob.toDateString() : 'Select DOB'}
                </Text>
              </TouchableOpacity>
              {errors.dob && <Text style={styles.error}>{errors.dob}</Text>}

              {showDate && (
                <DateTimePicker
                  value={dob}
                  mode="date"
                  display="default"
                  onChange={(event, selectedDate) => {
                    const currentDate = selectedDate || dob;
                    setShowDate(false);
                    setDob(currentDate);
                  }}
                  maximumDate={new Date()}
                />
              )}

              <TouchableOpacity
                style={[styles.button, loading && styles.buttonDisabled]}
                onPress={handleSave}
                disabled={loading}
              >
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color="#fff" />
                    <Text style={[styles.buttonText, { marginLeft: 10 }]}>Saving...</Text>
                  </View>
                ) : (
                  <Text style={styles.buttonText}>Save & Continue</Text>
                )}
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </ScrollView>

        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={hideToast}
          />
        )}
      </LinearGradient>
    </SafeAreaView>
  );
};

export default CompleteProfile;

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  overlay: {
    margin: 20,
    overflow: 'hidden',
  },
  glass: {
    padding: 30,
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  title: {
    fontSize: 24,
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.extraBold,
    marginBottom: 20,
    textAlign: 'center',
  },
  imagePicker: {
    alignSelf: 'center',
    marginBottom: 20,
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  pickerWrapper: {
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderRadius: 10,
    backgroundColor: 'rgba(255,255,255,0.1)',
    marginBottom: 10,
  },
  picker: {
    color: '#fff',
  },
  dateBtn: {
    height: 45,
    justifyContent: 'center',
    borderRadius: 10,
    paddingHorizontal: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    marginBottom: 10,
  },
  dateText: {
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.medium,
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  error: {
    color: 'red',
    fontSize: 12,
    marginBottom: 10,
    fontFamily: fontFamilies.POPPINS.medium,
  },
  circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
});
