import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import CustomInput from '../../component/CustomInput';
import { useDispatch, useSelector } from 'react-redux';
import { forgotPassword } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';

const Forgot = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { loading } = useSelector(state => state.auth || {});

  const [submitted, setSubmitted] = useState(false);
  const [email, setEmail] = useState('');
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => setToastData({ type, message });
  const hideToast = () => setToastData(null);

  const handleSubmit = async () => {
    setSubmitted(true);

    // Validation
    const emailValid = email.includes('@') && email.includes('.') && email.length > 5;

    if (!email || !emailValid) {
      showToast('error', 'Please enter a valid email');
      return;
    }

    try {
      console.log('Attempting to send forgot password email:', email);

      // Dispatch forgot password action
      const result = await forgotPassword(email)(dispatch);

      console.log('Forgot password result:', result);

      if (result.success) {
        showToast('success', 'OTP sent to your email!');
        // Navigate to OTP verification with email
        navigation.navigate('OTPVerification', {
          email: email,
          otp: result.data.otp // In real app, don't pass OTP
        });
      } else {
        showToast('error', result.message || 'Failed to send OTP');
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      showToast('error', 'Failed to send OTP. Please try again.');
    }
  };
  return (
    <>
      <LinearGradient
        colors={['#333399', '#ff00cc']}
        style={styles.background}
      >
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />
        <View style={styles.overlay}>
          {Platform.OS === 'ios' && (
            <BlurView style={styles.glass} blurType="light" blurAmount={20} />
          )}
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.glass}
          >
            <Text style={styles.title}>Forgot Password</Text>
            <CustomInput
              label='Email'
              keyboardType='email-address' 
              value={email}
              onChangeText={setEmail}
              iconLeft="mail-outline" 
              placeholder="<EMAIL>"
              error={
                submitted && !email
                  ? 'Email is required'
                  : submitted && !(email.includes('@') && email.includes('.') && email.length > 5)
                    ? 'Enter a valid email'
                    : ''
              }
            />
            <TouchableOpacity
              onPress={handleSubmit}
              style={[styles.button, loading && styles.buttonDisabled]}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={[styles.buttonText, { marginLeft: 10 }]}>Sending OTP...</Text>
                </View>
              ) : (
                <Text style={styles.buttonText}>Continue</Text>
              )}
            </TouchableOpacity>
            {/* <TouchableOpacity onPress={() => navigation.navigate('Forgot')}>
            <Text style={styles.forgot}>Forgot Password?</Text>
            </TouchableOpacity> */}
          </LinearGradient>
        </View>
      </LinearGradient>
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

export default Forgot;

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
  },
  overlay: {
    margin: 20,
    // borderRadius: 20,
    overflow: 'hidden',
  },
  glass: {
    padding: 30,
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  title: {
    fontSize: 28,
    color: '#fff',
    marginBottom: 30,
    fontStyle: 'italic',
    fontFamily: fontFamilies.POPPINS.extraBold,
    textAlign: 'center',
  },
  input: {
    height: 45,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    color: '#fff',
    marginBottom: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  forgot: {
    color: Color.Secondary,
    textAlign: 'center',
    marginTop: 15,
    fontFamily: fontFamilies.POPPINS.regular,
    textDecorationLine: 'underline',
  },
});
