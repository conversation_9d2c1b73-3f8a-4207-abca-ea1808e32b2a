import React, { useState } from 'react';
import {
  Image, Modal, StyleSheet, Text, TouchableOpacity, View, ScrollView,
  PermissionsAndroid, Platform, Alert, ToastAndroid, Linking, Share
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../constant/color';
import Like from '../assets/image/like.svg';
import LikeForDark from '../assets/image/LikeForDark.svg';
import CommentModal from './CommentReplyModal';
import SavePost from '../assets/image/svaePost.svg';
import { fontFamilies } from '../constant/Font';
import RNFS from 'react-native-fs';
import { useNavigation } from '@react-navigation/native';


const LikeCommComponent = ({ musicImage, ProfileImage, videoData, onDeleteVideo }) => {
  const [showModal, setShowModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likecount, setLikeCount] = useState(0);
  const [save, setSave] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const navigation = useNavigation();

  const [comments, setComments] = useState([
    {
      id: 1,
      name: 'Abdul manan',
      text: 'drama ka name Kia ha',
      avatar: 'https://i.pravatar.cc/150?img=1',
      replies: [],
      showReplies: false,
    },
    {
      id: 2,
      name: 'Amir Shahzad',
      text: 'My favrt drama sher and fajar',
      avatar: 'https://i.pravatar.cc/150?img=2',
      replies: [],
      showReplies: false,
    },
  ]);

  const handleLikes = () => {
    setIsLiked(prev => {
      setLikeCount(count => prev ? count - 1 : count + 1);
      return !prev;
    });
  };

  // Get video URL from props or use default
  const videoUrl = videoData?.videoUrl || 'https://www.w3schools.com/html/mov_bbb.mp4';

  // Download video function
  const downloadVideo = async () => {
    if (downloading) return;

    const hasPermission = await requestStoragePermission();

    if (!hasPermission) {
      Alert.alert('Permission Denied!', 'Storage permission is required to download videos');
      return;
    }

    setDownloading(true);
    try {
      const fileName = `video_${Date.now()}.mp4`;
      const downloadDest = `${RNFS.DownloadDirectoryPath}/${fileName}`;

      const download = RNFS.downloadFile({
        fromUrl: videoUrl,
        toFile: downloadDest,
        progressDivider: 1,
        begin: (res) => {
          console.log('Download started');
        },
        progress: (res) => {
          const progress = (res.bytesWritten / res.contentLength) * 100;
          console.log(`Download progress: ${progress.toFixed(2)}%`);
        },
      });

      const result = await download.promise;

      if (result.statusCode === 200) {
        if (Platform.OS === 'android') {
          ToastAndroid.show('Video downloaded successfully!', ToastAndroid.LONG);
        } else {
          Alert.alert('Success', 'Video downloaded successfully!');
        }
        setShowShareModal(false);
      } else {
        Alert.alert('Download failed', 'Unable to download the video.');
      }
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Error', 'Something went wrong while downloading.');
    } finally {
      setDownloading(false);
    }
  };

  // Share to WhatsApp function
 const shareToWhatsApp = async () => {
  try {
    const message = `Check out this amazing video!\n${videoUrl}`;
    const encodedMessage = encodeURIComponent(message);

    const whatsappPackages = [
      'whatsapp://send?text=',
      'whatsapp-business://send?text=',
    ];

    let supported = false;

    for (const scheme of whatsappPackages) {
      const url = `${scheme}${encodedMessage}`;
      const canOpen = await Linking.canOpenURL(url);

      if (canOpen) {
        supported = true;
        await Linking.openURL(url);
        setShowShareModal(false);
        break;
      }
    }

    if (!supported) {
      Alert.alert(
        'WhatsApp not installed',
        'Please install WhatsApp or WhatsApp Business to share the video.'
      );
    }
  } catch (error) {
    console.error('WhatsApp share error:', error);
    Alert.alert('Error', 'Unable to share to WhatsApp');
  }
};


  const shareVideo = async () => {
    try {
      const result = await Share.share({
        message: `Check out this amazing video!\n${videoUrl}`,
        url: videoUrl,
        title: 'Share Video',
      });

      if (result.action === Share.sharedAction) {
        setShowShareModal(false);
      }
    } catch (error) {
      console.error('Share error:', error);
      Alert.alert('Error', 'Unable to share video');
    }
  };

  // Delete video function
  const deleteVideo = () => {
    Alert.alert(
      'Delete Video',
      'Are you sure you want to delete this video?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            if (onDeleteVideo) {
              onDeleteVideo(videoData?.id);
            }
            setShowShareModal(false);
            if (Platform.OS === 'android') {
              ToastAndroid.show('Video deleted', ToastAndroid.SHORT);
            } else {
              Alert.alert('Success', 'Video deleted successfully');
            }
          },
        },
      ]
    );
  };

  const handleReport = () => {
   navigation.navigate('ReportScreen')
  };

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        ]);

        return (
          granted['android.permission.WRITE_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED &&
          granted['android.permission.READ_EXTERNAL_STORAGE'] === PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (err) {
        console.warn(err);
        return false;
      }
    } else {
      return true; // iOS handles differently
    }
  };


  return (
    <>
      <View style={styles.overlay}>
        <View style={styles.icon}>
          <Image source={ProfileImage} style={styles.musicContanier} />
        </View>

        <TouchableOpacity onPress={handleLikes} style={styles.icon}>
          {/* {isLiked ? <Like /> : <LikeForDark />} */}
          <Icon name={isLiked ? "heart" : "heart"} size={30} color={isLiked ? 'red' : Color.Secondary} />
          <Text style={styles.iconLabel}>{likecount}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.icon} onPress={() => setShowModal(true)}>
          <Icon name="chatbubble-ellipses" size={30} color={Color.Secondary} />
          <Text style={styles.iconLabel}>{comments.length}</Text>
        </TouchableOpacity>

        <TouchableOpacity onPress={() => setSave(!save)} style={styles.icon}>
          {save ? (
            <SavePost width={30} height={30} />
          ) : (
            <Icon name="bookmark" size={30} color={Color.Secondary} />
          )}
          <Text style={styles.iconLabel}>Save</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.icon} onPress={() => setShowShareModal(true)}>
          <Icon name="share-social" size={30} color={Color.Secondary} />
          <Text style={styles.iconLabel}>Share</Text>
        </TouchableOpacity>

        <View style={styles.icon}>
          <Image source={musicImage} style={styles.musicContanier} />
        </View>
      </View>

      <CommentModal
        comments={comments}
        setComments={setComments}
        visible={showModal}
        onClose={() => setShowModal(false)}
      />

      <Modal
        animationType="slide"
        transparent
        visible={showShareModal}
        onRequestClose={() => setShowShareModal(false)}
      >
        <View style={styles.shareOverlay}>
          <View style={styles.shareModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Send to</Text>
              <TouchableOpacity onPress={() => setShowShareModal(false)}>
                <Icon style={styles.CloseIcon} name="close" size={24} color="#000" />
              </TouchableOpacity>
            </View>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.contactRow}>
              {[...Array(8)].map((_, i) => (
                <View key={i} style={styles.contactItem}>
                  <Image
                    source={{ uri: `https://i.pravatar.cc/150?img=${i + 1}` }}
                    style={styles.contactAvatar}
                  />
                  <Text numberOfLines={1} style={styles.contactName}>User {i + 1}</Text>
                </View>
              ))}

            </ScrollView>
            <View style={{ borderColor: Color.inputBg, borderWidth: 0.5, marginBottom: 15 }} />

            {/* <View style={styles.shereContainer}>
              <TouchableOpacity onPress={shareToWhatsApp} style={{ alignItems: 'center', gap: 2 }}>
                <Image source={require('../assets/image/whatsAppImage.webp')} style={{ width: 40, height: 40, borderRadius: 40 }} />
                <Text style={styles.Text}>WhatsApp</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={shareVideo} style={{ alignItems: 'center', gap: 2 }}>
                <Image source={require('../assets/image/facebookImage.png')} style={{ width: 40, height: 40, borderRadius: 40 }} />
                <Text style={styles.Text}>FaceBook</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={shareVideo} style={{ alignItems: 'center', gap: 2 }}>
                <Image source={require('../assets/image/instaImage.webp')} style={{ width: 40, height: 40, borderRadius: 40 }} />
                <Text style={styles.Text}>Instagram</Text>
              </TouchableOpacity>
            </View> */}
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.shereContainer1}>
              <TouchableOpacity onPress={handleReport} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name='flag-outline' size={20} color={Color.Primary} />
                </View>
                <Text style={styles.Text}>Report</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                setShowShareModal(false);
                if (Platform.OS === 'android') {
                  ToastAndroid.show('Marked as not interested', ToastAndroid.SHORT);
                }
              }} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name='eye-off-outline' size={20} color={Color.Primary} />
                </View>
                <Text style={styles.Text}>Not Interested</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={downloadVideo} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: downloading ? Color.purple : Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name={downloading ? 'hourglass-outline' : 'download-outline'} size={20} color={Color.Primary} />
                </View>
                <Text style={styles.Text}>{downloading ? 'Downloading...' : 'Download'}</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                setShowShareModal(false);
                if (Platform.OS === 'android') {
                  ToastAndroid.show('Video marked as private', ToastAndroid.SHORT);
                }
              }} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name='lock-closed-outline' size={20} color={Color.Primary} />
                </View>
                <Text style={styles.Text}>Private</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={deleteVideo} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name='trash-outline' size={20} color='#ff4444' />
                </View>
                <Text style={[styles.Text, { color: '#ff4444' }]}>Delete</Text>
              </TouchableOpacity>
               <TouchableOpacity onPress={shareVideo} style={{ alignItems: 'center', gap: 2 }}>
                <View style={{ width: 40, height: 40, borderRadius: 40, backgroundColor: Color.inputBg, alignItems: 'center', justifyContent: 'center' }}>
                  <Icon name='ellipsis-horizontal-outline' size={20} color={Color.Primary} />
                </View>
                <Text style={[styles.Text, { color: Color.Primary }]}>More</Text>
              </TouchableOpacity>
            </View>
</ScrollView>
          </View>
        </View>
      </Modal>
    </>
  );
};

export default LikeCommComponent;

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    right: 8,
    top: '43%',
    alignItems: 'center',
  },
  icon: {
    marginBottom: 10,
    alignItems: 'center',
  },
  iconLabel: {
    color: Color.Secondary,
    marginTop: 5,
    fontSize: 12,
    // fontWeight: '500',
    fontFamily: fontFamilies.POPPINS.semiBold,
  },
  musicContanier: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: Color.Secondary
  },
  shareOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'flex-end',
  },
  shareModal: {
    backgroundColor: Color.Secondary,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: '65%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  modalTitle: {
    fontSize: 20,
    color: Color.Primary,
    fontFamily: fontFamilies.POPPINS.extraBold
  },
  contactRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  contactItem: {
    alignItems: 'center',
    marginRight: 7,
    width: 60,
  },
  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: 4,
  },
  contactName: {
    fontSize: 12,
    color: Color.Primary,
    fontFamily: fontFamilies.POPPINS.semiBold,
    textAlign: 'center',
  },
  shareGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  shareItem: {
    width: '22%',
    alignItems: 'center',
    marginBottom: 20,
  },
  shareLabel: {
    fontSize: 11,
    marginTop: 4,
    textAlign: 'center',
  },
  shereContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    // marginHorizontal: 
  },
  shereContainer1: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    marginHorizontal: 6,
    marginVertical: 20,
  },
  Text: {
    color: Color.Primary,
    fontSize: 11,
    fontFamily: fontFamilies.POPPINS.semiBold,
  },
});
