
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';
import ForyouVideoComponent from './ForyouVideoComponent';
import CustomToast from './CustomToast';

// Placeholder async fetch function (replace with real API call)
const fetchVideos = async () => {
  // Simulate network delay
  await new Promise(res => setTimeout(res, 600));
  return [
    {
      id: 1,
      tittle: 'Amazing Nature Video',
      description: 'Beautiful scenery from around the world #nature #beautiful #travel',
      videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4',
    },
    {
      id: 2,
      tittle: 'Funny Cat Video',
      description: 'Hilarious cat compilation #cats #funny #pets #viral',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    },
  ];
};

const deleteVideoAPI = async (videoId) => {
  // Simulate network delay and success
  await new Promise(res => setTimeout(res, 400));
  return { success: true };
};

const ShareModalDemo = () => {
  const [videos, setVideos] = useState([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [toastData, setToastData] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  const mockProfileImage = require('../assets/image/music.jpg');
  const mockMusicImage = require('../assets/image/music.jpg');

  useEffect(() => {
    const loadVideos = async () => {
      setLoading(true);
      try {
        const data = await fetchVideos();
        setVideos(data);
      } catch (err) {
        setToastData({ type: 'error', message: 'Failed to load videos' });
      } finally {
        setLoading(false);
      }
    };
    loadVideos();
  }, []);

  const handleDeleteVideo = async (videoId) => {
    setActionLoading(true);
    try {
      const result = await deleteVideoAPI(videoId);
      if (result.success) {
        setVideos(prev => prev.filter(v => v.id !== videoId));
        setToastData({ type: 'success', message: 'Video deleted!' });
        // Adjust index if needed
        setCurrentVideoIndex(idx => {
          if (videos[idx]?.id === videoId) {
            if (idx >= videos.length - 1) {
              return Math.max(0, idx - 1);
            }
          }
          return idx;
        });
      } else {
        setToastData({ type: 'error', message: 'Delete failed' });
      }
    } catch (err) {
      setToastData({ type: 'error', message: 'Delete failed' });
    } finally {
      setActionLoading(false);
    }
  };

  const handleReset = async () => {
    setActionLoading(true);
    try {
      const data = await fetchVideos();
      setVideos(data);
      setCurrentVideoIndex(0);
      setToastData({ type: 'success', message: 'Videos reset!' });
    } catch {
      setToastData({ type: 'error', message: 'Reset failed' });
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.emptyContainer, { justifyContent: 'center' }] }>
        <ActivityIndicator size="large" color={Color.purple} />
        <Text style={styles.emptyText}>Loading videos...</Text>
      </View>
    );
  }

  if (videos.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No videos available</Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={handleReset}
          disabled={actionLoading}
        >
          {actionLoading ? <ActivityIndicator color="#fff" /> : <Text style={styles.resetButtonText}>Reset Videos</Text>}
        </TouchableOpacity>
        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={() => setToastData(null)}
          />
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Share Modal Demo</Text>
        <Text style={styles.subtitle}>
          Tap the share button to test video sharing, download, and delete features
        </Text>
      </View>

      <View style={styles.videoContainer}>
        {videos[currentVideoIndex] && (
          <ForyouVideoComponent
            item={{ uri: videos[currentVideoIndex].videoUrl }}
            ProfileImage={mockProfileImage}
            musicImage={mockMusicImage}
            isPlay={true}
            items={videos[currentVideoIndex]}
            onDeleteVideo={handleDeleteVideo}
          />
        )}
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, currentVideoIndex === 0 && styles.disabledButton]}
          onPress={() => setCurrentVideoIndex(Math.max(0, currentVideoIndex - 1))}
          disabled={currentVideoIndex === 0 || actionLoading}
        >
          <Text style={styles.controlButtonText}>Previous</Text>
        </TouchableOpacity>

        <Text style={styles.videoCounter}>
          {currentVideoIndex + 1} of {videos.length}
        </Text>

        <TouchableOpacity
          style={[styles.controlButton, currentVideoIndex === videos.length - 1 && styles.disabledButton]}
          onPress={() => setCurrentVideoIndex(Math.min(videos.length - 1, currentVideoIndex + 1))}
          disabled={currentVideoIndex === videos.length - 1 || actionLoading}
        >
          <Text style={styles.controlButtonText}>Next</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Share Modal Features:</Text>
        
        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>📱 WhatsApp Sharing</Text>
          <Text style={styles.featureDescription}>
            Tap WhatsApp icon to share video link directly to WhatsApp
          </Text>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>⬇️ Video Download</Text>
          <Text style={styles.featureDescription}>
            Download videos to your device storage with progress indication
          </Text>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>🗑️ Delete Video</Text>
          <Text style={styles.featureDescription}>
            Remove videos from your feed with confirmation dialog
          </Text>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>🚫 Report & Block</Text>
          <Text style={styles.featureDescription}>
            Report inappropriate content or mark as not interested
          </Text>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>🔒 Privacy Controls</Text>
          <Text style={styles.featureDescription}>
            Mark videos as private or control visibility
          </Text>
        </View>

        <View style={styles.featureItem}>
          <Text style={styles.featureTitle}>📤 Social Sharing</Text>
          <Text style={styles.featureDescription}>
            Share to Instagram, Facebook, and other social platforms
          </Text>
        </View>
      </ScrollView>
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={() => setToastData(null)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  title: {
    fontSize: 24,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    textAlign: 'center',
    lineHeight: 20,
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  controlButton: {
    backgroundColor: Color.purple,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  disabledButton: {
    backgroundColor: Color.inputBg,
    opacity: 0.5,
  },
  controlButtonText: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.medium,
    fontSize: 12,
  },
  videoCounter: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.medium,
    fontSize: 14,
  },
  infoContainer: {
    maxHeight: 200,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    paddingHorizontal: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.semiBold,
    color: Color.Secondary,
    marginVertical: 12,
  },
  featureItem: {
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.semiBold,
    color: Color.Secondary,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    lineHeight: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Color.Main,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.medium,
    color: Color.gray,
    marginBottom: 20,
    textAlign: 'center',
  },
  resetButton: {
    backgroundColor: Color.purple,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  resetButtonText: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.semiBold,
    fontSize: 16,
  },
});

export default ShareModalDemo;
