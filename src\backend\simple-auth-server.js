const express = require('express');
const cors = require('cors');

console.log('🚀 Starting Simple Auth Server...');

const app = express();

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

console.log('✅ Middleware configured');

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root route accessed');
  res.json({ 
    message: 'Simple Auth Server is working!',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Register API
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register API called:', req.body);
  
  const { firstName, lastName, email, password, phone, address } = req.body;
  
  if (!firstName || !lastName || !email || !password || !phone || !address) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  // Mock successful registration
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: 'mock-token-' + Date.now(),
    user: {
      id: 'user-' + Date.now(),
      name: `${firstName} ${lastName}`,
      firstName,
      lastName,
      email,
      phone,
      address,
      isVerified: false,
      isProfileComplete: false
    }
  });
});

// Login API
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login API called:', req.body);
  
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  // Mock successful login
  res.json({
    success: true,
    message: 'Login successful',
    token: 'mock-token-' + Date.now(),
    user: {
      id: 'user-123',
      name: 'Test User',
      email: email,
      phone: '1234567890',
      address: '123 Test Street',
      isVerified: true,
      isProfileComplete: true
    }
  });
});

// Complete Profile API
app.post('/api/auth/complete-profile', (req, res) => {
  console.log('👤 Complete Profile API called:', req.body);
  
  res.json({
    success: true,
    message: 'Profile completed successfully',
    user: {
      id: 'user-123',
      name: 'Test User',
      bio: req.body.bio || 'Test bio',
      gender: req.body.gender || 'male',
      isProfileComplete: true
    }
  });
});

// Forgot Password API
app.post('/api/auth/forgot-password', (req, res) => {
  console.log('🔑 Forgot Password API called:', req.body);
  
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  // Mock OTP generation
  const otp = '123456';
  
  res.json({
    success: true,
    message: 'OTP sent to your email',
    otp: otp // In real app, don't send OTP in response
  });
});

// Verify OTP API
app.post('/api/auth/verify-otp', (req, res) => {
  console.log('🔢 Verify OTP API called:', req.body);
  
  const { email, otp } = req.body;
  
  if (!email || !otp) {
    return res.status(400).json({
      success: false,
      message: 'Email and OTP are required'
    });
  }

  // Mock OTP verification (accept 123456)
  if (otp === '123456') {
    res.json({
      success: true,
      message: 'OTP verified successfully'
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Invalid OTP'
    });
  }
});

// Reset Password API
app.post('/api/auth/reset-password', (req, res) => {
  console.log('🔐 Reset Password API called:', req.body);
  
  const { email, otp, newPassword } = req.body;
  
  if (!email || !otp || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Email, OTP and new password are required'
    });
  }

  // Mock password reset
  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

const PORT = 3002; // Changed port to avoid conflicts

// Middleware to verify token
const verifyToken = (req, res, next) => {
  console.log('🔐 Token verification - Headers:', req.headers.authorization);
  
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  console.log('🔐 Extracted token:', token);
  
  if (!token) {
    console.log('❌ No token provided');
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }
  
  // Mock token verification (accept any token format)
  if (token.length < 10) {
    console.log('❌ Token too short:', token);
    return res.status(403).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  console.log('✅ Token verified successfully');
  req.user = { id: 'user-123', email: '<EMAIL>' };
  next();
};

// Posts API endpoints
app.get('/api/posts', verifyToken, (req, res) => {
  console.log('📝 Get Posts API called');
  
  res.json({
    success: true,
    posts: [
      {
        id: '1',
        userId: 'user-123',
        userName: 'Test User',
        content: 'This is a sample post',
        image: null,
        likes: 5,
        comments: 2,
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        userId: 'user-456',
        userName: 'Another User',
        content: 'Another sample post with some content',
        image: null,
        likes: 10,
        comments: 3,
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.post('/api/posts', verifyToken, (req, res) => {
  console.log('📝 Create Post API called:', req.body);
  
  const { content, image } = req.body;
  
  res.json({
    success: true,
    message: 'Post created successfully',
    post: {
      id: 'post-' + Date.now(),
      userId: req.user.id,
      userName: 'Test User',
      content: content,
      image: image,
      likes: 0,
      comments: 0,
      createdAt: new Date().toISOString()
    }
  });
});

app.post('/api/posts/:postId/like', verifyToken, (req, res) => {
  console.log('❤️ Like Post API called for post:', req.params.postId);
  
  res.json({
    success: true,
    message: 'Post liked successfully',
    liked: true
  });
});

app.get('/api/posts/:postId/comments', verifyToken, (req, res) => {
  console.log('💬 Get Comments API called for post:', req.params.postId);
  
  res.json({
    success: true,
    comments: [
      {
        id: '1',
        userId: 'user-123',
        userName: 'Test User',
        text: 'Great post!',
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.post('/api/posts/:postId/comments', verifyToken, (req, res) => {
  console.log('💬 Add Comment API called:', req.body);
  
  const { text } = req.body;
  
  res.json({
    success: true,
    message: 'Comment added successfully',
    comment: {
      id: 'comment-' + Date.now(),
      userId: req.user.id,
      userName: 'Test User',
      text: text,
      createdAt: new Date().toISOString()
    }
  });
});

// Users API endpoints
app.get('/api/users/search', verifyToken, (req, res) => {
  console.log('🔍 Search Users API called:', req.query);
  
  const { query } = req.query;
  
  res.json({
    success: true,
    users: [
      {
        id: 'user-456',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: null,
        isFollowing: false
      },
      {
        id: 'user-789',
        name: 'Jane Smith',
        email: '<EMAIL>',
        profileImage: null,
        isFollowing: true
      }
    ]
  });
});

app.get('/api/users/:userId', verifyToken, (req, res) => {
  console.log('👤 Get User Profile API called for user:', req.params.userId);
  
  res.json({
    success: true,
    user: {
      id: req.params.userId,
      name: 'Test User',
      email: '<EMAIL>',
      bio: 'This is a test bio',
      profileImage: null,
      followersCount: 10,
      followingCount: 15,
      postsCount: 5,
      isFollowing: false
    }
  });
});

app.post('/api/users/:userId/follow', verifyToken, (req, res) => {
  console.log('👥 Follow User API called for user:', req.params.userId);
  
  res.json({
    success: true,
    message: 'User followed successfully',
    isFollowing: true
  });
});

app.get('/api/users/:userId/followers', verifyToken, (req, res) => {
  console.log('👥 Get Followers API called for user:', req.params.userId);
  
  res.json({
    success: true,
    followers: [
      {
        id: 'user-456',
        name: 'John Doe',
        profileImage: null
      }
    ]
  });
});

app.get('/api/users/:userId/following', verifyToken, (req, res) => {
  console.log('👥 Get Following API called for user:', req.params.userId);
  
  res.json({
    success: true,
    following: [
      {
        id: 'user-789',
        name: 'Jane Smith',
        profileImage: null
      }
    ]
  });
});

// Mock following list (in real app, get from database)
const mockFollowing = {
  'user-123': ['user-456', 'user-789'], // current user follows these users
  'user-456': ['user-123'],
  'user-789': ['user-123']
};

// Helper function to check if users are friends (mutual following)
const areFriends = (userId1, userId2) => {
  const user1Following = mockFollowing[userId1] || [];
  const user2Following = mockFollowing[userId2] || [];
  
  // Check if they follow each other (mutual following = friends)
  return user1Following.includes(userId2) && user2Following.includes(userId1);
};

// Chat API endpoints
app.get('/api/chats', verifyToken, (req, res) => {
  console.log('💬 Get Chats API called');
  
  // Only return chats with friends
  const userFollowing = mockFollowing[req.user.id] || [];
  const friendChats = [];
  
  // Filter chats to only include friends
  userFollowing.forEach(friendId => {
    if (areFriends(req.user.id, friendId)) {
      friendChats.push({
        id: `chat-${friendId}`,
        userId: friendId,
        userName: friendId === 'user-456' ? 'John Doe' : 'Jane Smith',
        lastMessage: 'Hello there!',
        lastMessageTime: new Date().toISOString(),
        unreadCount: 2
      });
    }
  });
  
  res.json({
    success: true,
    chats: friendChats
  });
});

app.get('/api/chats/:chatId/messages', verifyToken, (req, res) => {
  console.log('💬 Get Messages API called for chat:', req.params.chatId);
  
  // Extract userId from chatId (format: chat-userId)
  const otherUserId = req.params.chatId.replace('chat-', '');
  
  // Check if users are friends
  if (!areFriends(req.user.id, otherUserId)) {
    return res.status(403).json({
      success: false,
      message: 'You can only chat with friends. Follow each other first.'
    });
  }
  
  res.json({
    success: true,
    messages: [
      {
        id: 'msg-1',
        senderId: otherUserId,
        text: 'Hello there!',
        createdAt: new Date().toISOString()
      },
      {
        id: 'msg-2',
        senderId: req.user.id,
        text: 'Hi! How are you?',
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.post('/api/chats/:chatId/messages', verifyToken, (req, res) => {
  console.log('💬 Send Message API called:', req.body);
  
  const { text } = req.body;
  const otherUserId = req.params.chatId.replace('chat-', '');
  
  // Check if users are friends before allowing message send
  if (!areFriends(req.user.id, otherUserId)) {
    return res.status(403).json({
      success: false,
      message: 'You can only send messages to friends. Follow each other first.'
    });
  }
  
  res.json({
    success: true,
    message: 'Message sent successfully',
    messageData: {
      id: 'msg-' + Date.now(),
      senderId: req.user.id,
      text: text,
      createdAt: new Date().toISOString()
    }
  });
});

// Notification API endpoints
app.get('/api/notifications', verifyToken, (req, res) => {
  console.log('🔔 Get Notifications API called');
  
  // Mock notifications data
  const notifications = [
    {
      id: 'notif-1',
      type: 'like',
      title: 'New Like',
      message: 'John Doe liked your post',
      isRead: false,
      createdAt: new Date().toISOString(),
      data: {
        postId: 'post-123',
        userId: 'user-456'
      }
    },
    {
      id: 'notif-2',
      type: 'follow',
      title: 'New Follower',
      message: 'Jane Smith started following you',
      isRead: false,
      createdAt: new Date(Date.now() - 3600000).toISOString(),
      data: {
        userId: 'user-789'
      }
    },
    {
      id: 'notif-3',
      type: 'comment',
      title: 'New Comment',
      message: 'Mike Johnson commented on your post',
      isRead: true,
      createdAt: new Date(Date.now() - 7200000).toISOString(),
      data: {
        postId: 'post-456',
        userId: 'user-321',
        comment: 'Great post!'
      }
    }
  ];
  
  res.json({
    success: true,
    notifications: notifications
  });
});

app.post('/api/notifications/send', verifyToken, (req, res) => {
  console.log('🔔 Send Notification API called:', req.body);
  
  const { type, title, message, toUserId, data } = req.body;
  
  // Mock notification sending
  const notification = {
    id: 'notif-' + Date.now(),
    type: type,
    title: title,
    message: message,
    fromUserId: req.user.id,
    toUserId: toUserId,
    isRead: false,
    createdAt: new Date().toISOString(),
    data: data
  };
  
  console.log('🔔 Notification created:', notification);
  
  res.json({
    success: true,
    message: 'Notification sent successfully',
    notification: notification
  });
});

app.post('/api/notifications/send-otp', (req, res) => {
  console.log('🔔 Send OTP Notification API called:', req.body);
  
  const { phoneNumber, otp, type, title, message } = req.body;
  
  // Mock OTP notification
  console.log(`📱 Sending OTP ${otp} to ${phoneNumber}`);
  
  res.json({
    success: true,
    message: 'OTP notification sent successfully',
    otp: otp
  });
});

app.put('/api/notifications/:notificationId/read', verifyToken, (req, res) => {
  console.log('🔔 Mark Notification Read API called for:', req.params.notificationId);
  
  res.json({
    success: true,
    message: 'Notification marked as read'
  });
});

// Generic success response for any other API calls
app.use('/api/*', verifyToken, (req, res) => {
  console.log(`📡 Generic API called: ${req.method} ${req.originalUrl}`);
  
  res.json({
    success: true,
    message: 'API endpoint working',
    data: req.body || {}
  });
});

app.listen(PORT, (err) => {
  if (err) {
    console.error('❌ Server failed to start:', err);
    return;
  }
  console.log(`🚀 Simple Auth Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`📍 Network: http://0.0.0.0:${PORT}`);
  console.log('✅ Server started successfully!');
});

console.log('📝 Server setup complete');
