import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';

const CustomInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  error = '',
  iconLeft = null,
  iconRight = null,
  onRightIconPress = null,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const [labelDisplayed, setLabelDisplayed] = useState('');
  const borderColorAnim = useRef(new Animated.Value(0)).current;

  // Typewriter label animation
  useEffect(() => {
    if (isFocused || value) {
      let i = 0;
      const interval = setInterval(() => {
        setLabelDisplayed(label.slice(0, i + 1));
        i++;
        if (i === label.length) clearInterval(interval);
      }, 40);
      return () => clearInterval(interval);
    } else {
      setLabelDisplayed('');
    }
  }, [isFocused, value]);

  // Border glow animation
  useEffect(() => {
    Animated.timing(borderColorAnim, {
      toValue: isFocused ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isFocused]);

  const borderColor = borderColorAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['rgba(255,255,255,0.3)', '#0ff'],
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.inputContainer,
          multiline && { height: numberOfLines * 45 },
          { borderColor },
        ]}
      >
        {iconLeft && (
          <Icon
            name={iconLeft}
            size={20}
            color="#fff"
            style={styles.leftIcon}
          />
        )}

        <TextInput
          style={[styles.input, multiline && { height: numberOfLines * 45 }]}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={Color.gray}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          secureTextEntry={!isPasswordVisible && secureTextEntry}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={numberOfLines}
        />

        {iconRight && (
          <TouchableOpacity
            style={styles.rightIcon}
            onPress={
              onRightIconPress
                ? onRightIconPress
                : () => setIsPasswordVisible(!isPasswordVisible)
            }
          >
            <Icon
              name={
                secureTextEntry
                  ? isPasswordVisible
                    ? 'eye'
                    : 'eye-off'
                  : iconRight
              }
              size={20}
              color="#fff"
            />
          </TouchableOpacity>
        )}

        {/* Animated Typewriter Label */}
        {(isFocused || value) && (
          <Text style={styles.animatedLabel}>{labelDisplayed}</Text>
        )}
      </Animated.View>

      {error ? <Text style={styles.error}>{error}</Text> : null}
    </View>
  );
};

export default CustomInput;

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    paddingLeft: 40,
    paddingRight: 40,
    height: 50,
  },
  input: {
    flex: 1,
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.medium,
    fontSize: 14,
  },
  animatedLabel: {
    position: 'absolute',
    top: -15,
    left: 10,
    fontSize: 13,
    color: '#0ff',
    fontFamily: fontFamilies.POPPINS.medium,
  },
  leftIcon: {
    position: 'absolute',
    left: 12,
    top: 14,
  },
  rightIcon: {
    position: 'absolute',
    right: 10,
    top: 12,
  },
  error: {
    color: 'red',
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.medium,
    marginTop: 5,
    marginLeft: 5,
  },
});
