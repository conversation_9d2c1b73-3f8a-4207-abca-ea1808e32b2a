import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Color } from '../constant/color'
import { fontFamilies } from '../constant/Font'
import { useNavigation } from '@react-navigation/native'

const InboxChatCard = ({ item }) => {
  const navigation = useNavigation();
  const handlePress = () => {
    navigation.navigate('ChatScreen', { id: item.userId, name: item.name });
  };
  const profileImage = item.image
    ? { uri: item.image }
    : require('../assets/image/music.jpg');
  const lastMessage = item.lastMessage || '';
  const time = item.time
    ? new Date(item.time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : '';
  return (
    <TouchableOpacity onPress={handlePress}>
      <View style={styles.container}>
        <Image source={profileImage} style={{ width: 60, height: 60, borderRadius: 40 }} />
        <View style={{ flex: 0.9 }}>
          <Text style={styles.name}>{item.name}</Text>
          <Text style={styles.lastMessage}>{lastMessage}</Text>
        </View>
        <Text style={styles.data}>{time}</Text>
      </View>
      <View style={{ backgroundColor: Color.Main, borderWidth: 1, width: '90%', marginTop: 15, height: 3 }} />
    </TouchableOpacity>
  );
};

export default InboxChatCard

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
         alignItems: 'center',
         gap: 10
    }
    ,name:{
        color: Color.Secondary,
        fontSize : 15,
        fontFamily : fontFamilies.POPPINS.bold
    },
    data : {
          color: Color.Secondary,
        //   alignSelf: 'flex-end',
        fontSize : 12,
        fontFamily : fontFamilies.POPPINS.bold
    },
    lastMessage: {
          color: Color.Secondary,
        fontSize : 12,
        fontFamily : fontFamilies.POPPINS.extraLight
    }
})