// API Testing Script for Authentication APIs
// Run this with: node test_api.js

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/auth';
let authToken = '';

// Test data
const testUser = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  phone: '1234567890',
  address: '123 Main Street, City'
};

// Helper function to make API calls
const apiCall = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    console.log(`✅ ${method.toUpperCase()} ${endpoint}:`, response.data);
    return response.data;
  } catch (error) {
    console.log(`❌ ${method.toUpperCase()} ${endpoint}:`, error.response?.data || error.message);
    return null;
  }
};

// Test all APIs
const runTests = async () => {
  console.log('🚀 Starting API Tests...\n');

  // 1. Test Register
  console.log('1. Testing User Registration...');
  const registerResult = await apiCall('POST', '/register', testUser);
  if (registerResult && registerResult.token) {
    authToken = registerResult.token;
  }
  console.log('');

  // 2. Test Login
  console.log('2. Testing User Login...');
  const loginResult = await apiCall('POST', '/login', {
    email: testUser.email,
    password: testUser.password
  });
  if (loginResult && loginResult.token) {
    authToken = loginResult.token;
  }
  console.log('');

  // 3. Test Get Profile (Protected)
  console.log('3. Testing Get Profile...');
  await apiCall('GET', '/profile', null, authToken);
  console.log('');

  // 4. Test Update Profile (Protected)
  console.log('4. Testing Update Profile...');
  await apiCall('PUT', '/profile', {
    firstName: 'John',
    lastName: 'Smith',
    phone: '9876543210',
    address: '456 Oak Street, New City'
  }, authToken);
  console.log('');

  // 5. Test Verify Token (Protected)
  console.log('5. Testing Verify Token...');
  await apiCall('GET', '/verify-token', null, authToken);
  console.log('');

  // 6. Test Forgot Password
  console.log('6. Testing Forgot Password...');
  await apiCall('POST', '/forgot-password', {
    email: testUser.email
  });
  console.log('');

  // 7. Test Change Password (Protected)
  console.log('7. Testing Change Password...');
  await apiCall('POST', '/change-password', {
    currentPassword: testUser.password,
    newPassword: 'newpassword123'
  }, authToken);
  console.log('');

  // 8. Test Logout (Protected)
  console.log('8. Testing Logout...');
  await apiCall('POST', '/logout', null, authToken);
  console.log('');

  console.log('🏁 API Tests Completed!');
};

// Run the tests
runTests().catch(console.error);
