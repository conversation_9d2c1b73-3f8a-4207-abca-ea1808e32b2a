// FollowersScreen.js

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { getFollowers, followUser } from '../../redux/action/UserAction';
import CustomToast from '../../component/CustomToast';




const Follower = () => {
  const [search, setSearch] = useState('');
  const [toastData, setToastData] = useState(null);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { followers = [], loading } = useSelector(state => state.user || {});
  const { user } = useSelector(state => state.auth || {});

  useEffect(() => {
    if (user?.id) {
      dispatch(getFollowers(user.id));
    }
  }, [user?.id, dispatch]);

  const showToast = (type, message) => setToastData({ type, message });
  const hideToast = () => setToastData(null);

  const filteredData = followers.filter(item =>
    item.name?.toLowerCase().includes(search.toLowerCase())
  );

  const handleFollowBack = async (id, isFollowing) => {
    const result = await dispatch(followUser(id));
    if (result.success) {
      showToast('success', result.isFollowing ? 'Followed!' : 'Unfollowed!');
      dispatch(getFollowers(user.id));
    } else {
      showToast('error', result.error || 'Failed to follow');
    }
  };

  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <TouchableOpacity onPress={() => navigation.navigate('ViewInboxProfile', { userId: item.id })}>
        <Image
          source={
            item?.profileImage
              ? { uri: `http://192.168.100.150:3001/uploads/profiles/${item.profileImage}` }
              : require('../../assets/image/music.jpg')
          }
          style={styles.profileImage}
        />
      </TouchableOpacity>

      <View style={styles.textContainer}>
        <Text style={styles.name}>{item?.name}</Text>
        <Text style={styles.username}>{item?.bio || 'No bio available'}</Text>
        <Text style={styles.followersCount}>{item?.followersCount || 0} followers</Text>
      </View>

      <View style={styles.actionContainer}>
        {/* Chat Button */}
        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => navigation.navigate('ChatScreen', { userId: item.id, userName: item.name, userImage: item.profileImage })}
        >
          <Icon name="chatbubble-outline" size={20} color="#fff" />
        </TouchableOpacity>

        {/* Follow Button */}
        <LinearGradient colors={['#ff00cc', '#333399']} style={styles.followButton}>
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => handleFollowBack(item.id, item.isFollowing)}
          >
            <Text style={styles.followButtonText}>
              {item.isFollowing ? 'Following' : 'Follow'}
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={26} color={Color.Secondary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Followers</Text>
        <View style={{ width: 26 }} />
      </View>

      <View style={styles.searchBox}>
        <Icon name="search-outline" size={18} color="#888" />
        <TextInput
          style={styles.input}
          placeholder="Search"
          placeholderTextColor="#aaa"
          value={search}
          onChangeText={setSearch}
        />
      </View>

      <FlatList
        data={filteredData}
        renderItem={renderItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        refreshing={loading}
        onRefresh={() => user?.id && dispatch(getFollowers(user.id))}
      />
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default Follower;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
    paddingHorizontal: 12,
    paddingTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 18,
fontFamily : fontFamilies.POPPINS.extraBold,
    color: Color.Secondary,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    borderRadius: 10,
    paddingHorizontal: 10,
    marginBottom: 12,
    height: 38,
  },
  input: {
    marginLeft: 8,
    flex: 1,
    color: Color.Primary,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#ddd',
  },
  textContainer: {
    flex: 1,
    marginLeft: 10,
  },
  name: {
    color: Color.Secondary,
    fontSize: 15,
    fontFamily: fontFamilies.POPPINS.bold
  },
  username: {
    color: Color.inputBg,
    fontFamily: fontFamilies.POPPINS.regular,
    fontSize: 12,
  },
  button: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 6,
  },
  followBackBtn: {
    backgroundColor: Color.Primary,
  },
  friendsBtn: {
    backgroundColor: '#eee',
  },
});
