// FollowersScreen.js

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { getFollowing, followUser } from '../../redux/action/UserAction';
import CustomToast from '../../component/CustomToast';
import FollowingReelsComponent from '../../component/FollowingReelsComponent';




const FollowersScreen = () => {
  const [search, setSearch] = useState('');
  const [activeTab, setActiveTab] = useState('followers'); // 'followers' or 'reels'
  const [toastData, setToastData] = useState(null);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { following = [], loading } = useSelector(state => state.user || {});
  const { user } = useSelector(state => state.auth || {});

  useEffect(() => {
    if (user?.id) {
      dispatch(getFollowing(user.id));
    }
  }, [user?.id, dispatch]);

  const showToast = (type, message) => setToastData({ type, message });
  const hideToast = () => setToastData(null);

  const filteredData = following.filter(item =>
    item.name?.toLowerCase().includes(search.toLowerCase())
  );

  const handleFollowToggle = async (id, isFollowing) => {
    const result = await dispatch(followUser(id));
    if (result.success) {
      showToast('success', result.isFollowing ? 'Following' : 'Unfollowed');
      dispatch(getFollowing(user.id));
    } else {
      showToast('error', result.error || 'Failed to update follow');
    }
  };

  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <Image
        source={
          item?.profileImage
            ? { uri: `http://192.168.100.150:3001/uploads/profiles/${item.profileImage}` }
            : require('../../assets/image/music.jpg')
        }
        style={styles.profileImage}
      />
      <View style={styles.textContainer}>
        <Text style={styles.name}>{item?.name}</Text>
        <Text style={styles.username}>{item?.username}</Text>
      </View>

      <LinearGradient
        colors={['#ff00cc', '#333399']}
        style={styles.button}
      >
        <TouchableOpacity
          onPress={() => handleFollowToggle(item.id, item.isFollowing)}
          activeOpacity={0.7}
        >
          <Text style={styles.buttonText}>
            {item.isFollowing ? 'Unfollow' : 'Follow'}
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );

  const renderTabContent = () => {
    if (activeTab === 'reels') {
      return <FollowingReelsComponent />;
    }

    return (
      <>
        {/* Search Input */}
        <View style={styles.searchBox}>
          <Icon name="search-outline" size={18} color="#888" />
          <TextInput
            style={styles.input}
            placeholder="Search following"
            placeholderTextColor="#aaa"
            value={search}
            onChangeText={setSearch}
          />
        </View>

        {/* Following List */}
        <FlatList
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          refreshing={loading}
          onRefresh={() => user?.id && dispatch(getFollowing(user.id))}
        />
      </>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={26} color={Color.Secondary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Following</Text>
        <View style={{ width: 26 }} />
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'followers' && styles.activeTab]}
          onPress={() => setActiveTab('followers')}
        >
          <Text style={[styles.tabText, activeTab === 'followers' && styles.activeTabText]}>
            Followers
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'reels' && styles.activeTab]}
          onPress={() => setActiveTab('reels')}
        >
          <Text style={[styles.tabText, activeTab === 'reels' && styles.activeTabText]}>
            Reels
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {renderTabContent()}
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default FollowersScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
    paddingHorizontal: 12,
    paddingTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.extraBold,
    color: Color.Secondary,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: Color.Secondary,
  },
  tabText: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.medium,
    color: Color.inputBg,
  },
  activeTabText: {
    color: Color.Primary,
    fontFamily: fontFamilies.POPPINS.semiBold,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f1f1',
    borderRadius: 10,
    paddingHorizontal: 10,
    marginBottom: 12,
    height: 38,
  },
  input: {
    marginLeft: 8,
    flex: 1,
    color: Color.Primary,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#ddd',
  },
  textContainer: {
    flex: 1,
    marginLeft: 10,
  },
  name: {
    color: Color.Secondary,
    fontSize: 15,
    fontFamily: fontFamilies.POPPINS.bold,
  },
  username: {
    color: Color.inputBg,
    fontFamily: fontFamilies.POPPINS.regular,
    fontSize: 12,
  },
  button: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 6,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
});
