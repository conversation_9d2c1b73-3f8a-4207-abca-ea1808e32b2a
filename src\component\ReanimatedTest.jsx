import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
// Temporarily using React Native's built-in Animated API instead of Reanimated

const ReanimatedTest = () => {
  const translateX = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Using React Native's built-in Animated API
    const translateAnimation = Animated.loop(
      Animated.spring(translateX, {
        toValue: 100,
        useNativeDriver: true,
      })
    );

    const opacityAnimation = Animated.loop(
      Animated.timing(opacity, {
        toValue: 0.3,
        duration: 1000,
        useNativeDriver: true,
      })
    );

    translateAnimation.start();
    opacityAnimation.start();

    return () => {
      translateAnimation.stop();
      opacityAnimation.stop();
    };
  }, []);

  const animatedStyle = {
    transform: [{ translateX }],
    opacity,
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Animation Test</Text>
      <Animated.View style={[styles.box, animatedStyle]} />
      <Text style={styles.subtitle}>
        If you see a moving box, animations are working!
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    marginBottom: 20,
  },
  subtitle: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 20,
    textAlign: 'center',
  },
  box: {
    width: 50,
    height: 50,
    backgroundColor: '#007cf0',
    borderRadius: 10,
  },
});

export default ReanimatedTest;
