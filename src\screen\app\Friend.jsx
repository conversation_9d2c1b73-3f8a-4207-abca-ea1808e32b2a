import { Dimensions, FlatList, SafeAreaView, StyleSheet, View, StatusBar, ActivityIndicator, Text, RefreshControl } from 'react-native';
import React, { useRef, useState, useEffect } from 'react';
import Statusbar from '../../component/Statusbar';
import ForyouVideoComponent from '../../component/ForyouVideoComponent';
import { useDispatch, useSelector } from 'react-redux';
import { getFollowingPosts } from '../../redux/action/PostAction';
import CustomToast from '../../component/CustomToast';



const Friend = () => {
  const dispatch = useDispatch();
  const { posts, loading } = useSelector(state => state.posts || {});

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const viewabilityConfig = useRef({ viewAreaCoveragePercentThreshold: 50 }).current;

  const onViewRef = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setSelectedIndex(viewableItems[0].index);
    }
  });

  // Load following posts on component mount
  useEffect(() => {
    loadFollowingPosts();
  }, []);

  const loadFollowingPosts = async () => {
    try {
      const result = await getFollowingPosts()(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load following posts');
      }
    } catch (error) {
      console.error('Load following posts error:', error);
      showToast('error', 'Failed to load following posts');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFollowingPosts();
    setRefreshing(false);
  };

  // Only use backend posts, no dummy data
  const displayData = posts || [];

  const renderItem = ({ item, index }) => (
    <ForyouVideoComponent
      index={index}
      items={item}
      musicImage={item.musicImage || require('../../assets/image/music.jpg')}
      item={item.videoUrl || item.video}
      mediaType={item.mediaType || (item.video ? 'video' : item.image ? 'image' : 'video')}
      ProfileImage={item.user?.profileImage || require('../../assets/image/music.jpg')}
      isPlay={index === selectedIndex}
      postData={item}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="black" barStyle="light-content" />
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.loadingText}>Loading following posts...</Text>
        </View>
      ) : displayData.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No posts from friends yet</Text>
          <Text style={styles.emptySubText}>Follow some users to see their posts here</Text>
        </View>
      ) : (
        <FlatList
          data={displayData}
          renderItem={renderItem}
          keyExtractor={(item, index) => item.id?.toString() || index.toString()}
          showsVerticalScrollIndicator={false}
          pagingEnabled
          onViewableItemsChanged={onViewRef.current}
          viewabilityConfig={viewabilityConfig}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#fff"
            />
          }
        />
      )}

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default Friend;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000'
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000'
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
    paddingHorizontal: 20
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10
  },
  emptySubText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center'
  }
});
