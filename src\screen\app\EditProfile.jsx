import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-community/blur';
import Icon from 'react-native-vector-icons/Ionicons';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { updateProfile } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';
import AsyncStorage from '@react-native-async-storage/async-storage';

const EditProfileScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { user, loading } = useSelector(state => state.auth || {});

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [bio, setBio] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [toastData, setToastData] = useState(null);

  console.log('user==================================>', user)

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  useEffect(() => {
    const loadUserData = async () => {
      try {
        // First try Redux state
        if (user) {
          console.log('Loading user from Redux:', user);
          setFirstName(user.firstName || '');
          setLastName(user.lastName || '');
          setBio(user.bio || '');
          setPhone(user.phone || '');
          setAddress(user.address || '');
          return;
        }

        // If no Redux user, try AsyncStorage
        const storedUser = await AsyncStorage.getItem('userData');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          console.log('Loading user from AsyncStorage:', userData);
          setFirstName(userData.firstName || '');
          setLastName(userData.lastName || '');
          setBio(userData.bio || '');
          setPhone(userData.phone || '');
          setAddress(userData.address || '');
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    loadUserData();
  }, [user]);

  const handleSave = async () => {
    if (!firstName.trim() || !lastName.trim()) {
      showToast('error', 'First name and last name are required');
      return;
    }

    try {
      const profileData = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        bio: bio.trim(),
        phone: phone.trim(),
        address: address.trim(),
      };

      console.log('Updating profile with data:', profileData);

      const result = await updateProfile(profileData)(dispatch);

      console.log('Update profile result:', result);

      if (result.success) {
        showToast('success', 'Profile updated successfully!');
        setTimeout(() => {
          navigation.goBack();
        }, 1500);
      } else {
        showToast('error', result.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Update profile error:', error);
      showToast('error', 'Failed to update profile. Please try again.');
    }
  };

  return (
    <LinearGradient colors={['#333399', '#ff00cc']} style={styles.container}>
      <SafeAreaView style={{ flex: 1 }}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Glass Card */}
        <View style={styles.overlay}>
          {Platform.OS === 'ios' && (
            <BlurView style={styles.card} blurType="light" blurAmount={20} />
          )}
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.card}
          >
            {/* Profile Image */}
            <View style={styles.profileContainer}>
              <Image
                source={{ uri: 'https://i.pravatar.cc/150?img=13' }}
                style={styles.profileImage}
              />
              <TouchableOpacity style={styles.editIcon}>
                <Icon name="camera" size={18} color="#fff" />
              </TouchableOpacity>
            </View>

            {/* Form Inputs */}
            <CustomInput label="First Name" value={firstName} onChangeText={setFirstName} />
            <CustomInput label="Last Name" value={lastName} onChangeText={setLastName} />
            <CustomInput label="Bio" value={bio} onChangeText={setBio} multiline />
            <CustomInput label="Phone" value={phone} onChangeText={setPhone} keyboardType="phone-pad" />
            <CustomInput label="Address" value={address} onChangeText={setAddress} multiline />

            {/* Save Button */}
            <TouchableOpacity
              style={[styles.saveButton, loading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={[styles.saveButtonText, { marginLeft: 10 }]}>Saving...</Text>
                </View>
              ) : (
                <Text style={styles.saveButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
          </LinearGradient>
        </View>

        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={hideToast}
          />
        )}
      </SafeAreaView>
    </LinearGradient>
  );
};

const CustomInput = ({ label, ...rest }) => (
  <View style={{ marginBottom: 15 }}>
    <Text style={styles.label}>{label}</Text>
    <TextInput
      {...rest}
      style={styles.input}
      placeholderTextColor="#ccc"
      placeholder={label}
    />
  </View>
);

export default EditProfileScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginTop: '12%',
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.extraBold,
    color:Color.Secondary,
    textAlign: 'center',
  },
  overlay: {
    marginTop: 20,
    marginHorizontal: 20,
    overflow: 'hidden',
  },
  card: {
    padding: 20,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: Color.Secondary,
  },
  editIcon: {
    position: 'absolute',
    bottom: 0,
    right: 120 / 2 - 35,
    backgroundColor: Color.Primary,
    padding: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Color.Secondary,
  },
  label: {
    color: Color.inputBg,
    fontSize: 14,
    marginBottom: 4,
    fontFamily: fontFamilies.POPPINS.regular,
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderRadius: 10,
    padding: 10,
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.regular,
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  saveButton: {
    marginTop: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  saveButtonText: {
    color: Color.Secondary,
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  saveButtonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
