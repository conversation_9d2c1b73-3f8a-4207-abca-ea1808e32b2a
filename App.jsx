import React, { useEffect, useState } from 'react'
import SplashScreen from 'react-native-splash-screen';
import AppNavigator from './src/navigation/app_naviagte/AppNavigate';
import CustomSplashScreen from './src/component/CustomSplashScreen';
import { Provider } from 'react-redux';
import store from './src/redux/action/store';
import { Text, View } from 'react-native';
import OneSignalConfig from './src/config/OneSignalConfig';

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('App Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000' }}>
          <Text style={{ color: '#fff', fontSize: 18, marginBottom: 10 }}>Something went wrong</Text>
          <Text style={{ color: '#ccc', fontSize: 14, textAlign: 'center', paddingHorizontal: 20 }}>
            {this.state.error?.message || 'Unknown error occurred'}
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

const App = () => {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    SplashScreen.hide();
    
    // Initialize OneSignal
    OneSignalConfig.initialize();
    console.log('🔔 OneSignal initialized in App.jsx');

    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (showSplash) {
    return <CustomSplashScreen />;
  }

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <AppNavigator/>
      </Provider>
    </ErrorBoundary>
  )
}

export default App