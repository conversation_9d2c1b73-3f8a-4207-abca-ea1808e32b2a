// src/component/CustomToast.js
import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions, Image } from 'react-native';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';

const { width, height } = Dimensions.get('window');

const CustomToast = ({ type, message, onHide }) => {
  const slideAnim = new Animated.Value(100); // Start from bottom (100 px below)

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();

    const timer = setTimeout(() => {
      Animated.timing(slideAnim, {
        toValue: 100,
        duration: 300,
        useNativeDriver: true,
      }).start(() => onHide?.());
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        {
          backgroundColor: type === 'success' ? '#4BB543' : '#ff4d4d',
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
        <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-around'}}>
            <Image style={{width: 40, height: 40}} source={require("../assets/image/appIcon.webp")}/>
      <Text style={styles.toastText}>{message}</Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    bottom: 40, // Show near bottom
    alignSelf: 'center',
    zIndex: 9999,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
    minWidth: width * 0.9,
    alignItems: 'center',
    shadowColor: Color.Primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 6,
  },
  toastText: {
    color: Color.Secondary,
    fontSize: 15,
    fontFamily : fontFamilies.POPPINS.bold
},
});

export default CustomToast;
