import React from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  ScrollView,
  TouchableOpacity,
  Text,
} from 'react-native';
import RenderHTML from 'react-native-render-html';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';

const PolicyScreen = () => {
  const navigation = useNavigation();
  const policyHTML = `
    <h2>1. Information Collection</h2>
    <p>We may collect personal info such as your name, email, and usage data for better service.</p>
    
    <h2>2. Data Usage</h2>
    <p>We use the data to improve app functionality and user experience.</p>
    
    <h2>3. Third-party Services</h2>
    <p>We may use trusted third-party services that adhere to privacy standards.</p>
    
    <h2>4. Your Consent</h2>
    <p>By using the app, you consent to this privacy policy.</p>
    
    <h2>5. Contact Us</h2>
    <p>If you have questions, contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
  `;

  const { width } = Dimensions.get('window');

  return (
    <View style={{ flex: 1, backgroundColor: Color.Main }}>
      {/* Header with Back Button */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Policy</Text>
      </View>

      {/* HTML Content */}
      <ScrollView style={styles.container}>
        <RenderHTML
          contentWidth={width}
          source={{ html: policyHTML }}
          baseStyle={styles.text}
        />
      </ScrollView>
    </View>
  );
};

export default PolicyScreen;

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 9,
    marginTop: '10%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
    backgroundColor: Color.Main,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    textAlign: 'center',
    fontFamily: fontFamilies.POPPINS.semiBold,
    marginLeft: '25%',
  },
  container: {
    flex: 1,
    backgroundColor: Color.Main,
    paddingHorizontal: 11,
  },
  text: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.regular,
    fontSize: 14,
    lineHeight: 22,
  },
});
