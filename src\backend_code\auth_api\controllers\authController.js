const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const nodemailer = require('nodemailer');

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Generate JWT Token
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, process.env.JWT_SECRET, {
    expiresIn: '7d',
  });
};

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 1. REGISTER USER
exports.register = async (req, res) => {
  const { firstName, lastName, email, password, phone, address } = req.body;
  console.log('Register API called with:', { firstName, lastName, email, phone, address });

  try {
    // Validation
    if (!firstName || !lastName || !email || !password || !phone || !address) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }

    // Check if user already exists by email or phone
    let user = await User.findOne({ $or: [{ email }, { phone }] });
    if (user) {
      let duplicateField = user.email === email ? 'email' : 'phone';
      console.log(`Duplicate user found for ${duplicateField}:`, duplicateField === 'email' ? email : phone);
      return res.status(400).json({
        success: false,
        message: `User already exists with this ${duplicateField}`
      });
    } else {
      console.log('No duplicate user found, proceeding to create new user for email and phone:', email, phone);
    }

    // Hash password
    const hashed = await bcrypt.hash(password, 10);

    // Create user
    user = new User({
      name: `${firstName} ${lastName}`,
      firstName,
      lastName,
      email,
      password: hashed,
      phone,
      address
    });
    await user.save();
    console.log('User created and saved to DB:', user);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        address: user.address
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};

// 2. LOGIN USER
exports.login = async (req, res) => {
  const { email, password } = req.body;
  try {
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
    // Generate token
    const token = generateToken(user._id);
    res.status(200).json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        address: user.address
      }
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
};
