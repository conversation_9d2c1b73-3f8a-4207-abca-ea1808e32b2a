import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import CustomInput from '../../component/CustomInput';
import { useDispatch, useSelector } from 'react-redux';
import { loginUser } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';

const { width } = Dimensions.get('window');

const Login = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth || {});

  const [submitted, setSubmitted] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => setToastData({ type, message });
  const hideToast = () => setToastData(null);

  const handleSubmit = async () => {
    setSubmitted(true);

    // Validation
    const emailValid = email.includes('@') && email.includes('.') && email.length > 5;
    const passwordValid = password.length >= 8 && /[^A-Za-z0-9]/.test(password);

    if (!email || !emailValid || !password || !passwordValid) {
      showToast('error', 'Please fill all fields correctly');
      return;
    }

    try {
      console.log('Attempting to login user:', { email });

      // Dispatch login action
      const result = await loginUser(email, password)(dispatch);

      console.log('Login result:', result);

      if (result.success) {
        showToast('success', 'Login successful!');
        // Navigation will happen automatically through AppNavigator
        // when Redux state updates and isLoggedIn becomes true
      } else {
        showToast('error', result.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      showToast('error', 'Login failed. Please try again.');
    }
  };

  return (
    <>
      <LinearGradient
        colors={['#333399', '#ff00cc']}
        style={styles.container}
      >
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />
        <View style={styles.glassWrapper}>
          {Platform.OS === 'ios' ? (
            <BlurView style={styles.glass} blurType="dark" blurAmount={50} />
          ) : (
            <View style={[styles.glass, styles.androidGlass]} />
          )}
          <View style={styles.content}>
            <Text style={styles.title}>Welcome Back</Text>
            <CustomInput
              label='Email'
              keyboardType='email-address'
              value={email}
              onChangeText={setEmail}
              iconLeft="mail-outline"
              placeholder="<EMAIL>"
              error={
                submitted && !email
                  ? 'Email is required'
                  : submitted && !(email.includes('@') && email.includes('.') && email.length > 5)
                    ? 'Enter a valid email'
                    : ''
              }
            />
            <CustomInput
              label="Password"
              placeholder="password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              iconLeft="lock-closed-outline"
              iconRight="eye"
              error={
                submitted && !password
                  ? 'Password required'
                  : submitted && password.length < 8
                    ? 'Minimum 8 characters required'
                    : submitted && !/[^A-Za-z0-9]/.test(password)
                      ? 'At least 1 special character required'
                      : ''
              }
            />
            <TouchableOpacity
              onPress={handleSubmit}
              style={[styles.button, loading && styles.buttonDisabled]}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={[styles.buttonText, { marginLeft: 10 }]}>Logging in...</Text>
                </View>
              ) : (
                <Text style={styles.buttonText}>Login</Text>
              )}
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigation.navigate('Forgot')}>
              <Text style={styles.forgot}>Forgot Password?</Text>
            </TouchableOpacity>
            <View style={styles.signUpContainer}>
              <Text style={styles.forgot1}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('SignUp')}>
                <Text style={styles.SigninText}>Sign Up</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </LinearGradient>
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  glassWrapper: {
    width: width * 0.85,
    borderRadius: 25,
    overflow: 'hidden',
  },
  glass: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 25,
    borderColor: 'rgba(255,255,255,0.2)',
    borderWidth: 1,
  },
  androidGlass: {
    backgroundColor: 'rgba(255,255,255,0.08)',
  },
  content: {
    padding: 30,
  },
  title: {
    fontSize: 28,
    color: '#fff',
    marginBottom: 30,
    fontStyle: 'italic',
    fontFamily: fontFamilies.POPPINS.extraBold,
    textAlign: 'center',
  },
  input: {
    height: 45,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    color: '#fff',
    marginBottom: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  forgot: {
    color: Color.gray,
    textAlign: 'right',
    marginTop: 15,
    fontFamily: fontFamilies.POPPINS.regular,
    textDecorationLine: 'underline',
  },
  forgot1: {
    fontSize: 15,
    marginTop: 3,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.Secondary,
  },
  SigninText: {
    fontSize: 15,
    marginTop: 3,
    textDecorationLine: 'underline',
    color: '#00f',
    fontFamily: fontFamilies.POPPINS.regular,
  },
  signUpContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 15,
    justifyContent: 'center',
  },
});
