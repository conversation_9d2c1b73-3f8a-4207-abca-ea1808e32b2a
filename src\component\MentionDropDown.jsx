import React, { useState } from 'react';
import { View, Text, Image, TextInput, FlatList, StyleSheet } from 'react-native';

const dummyUsers = [
  { id: 1, name: '<PERSON><PERSON>', avatar: 'https://i.pravatar.cc/150?img=1' },
  { id: 2, name: '<PERSON>', avatar: 'https://i.pravatar.cc/150?img=2' },
  { id: 3, name: '<PERSON>ain <PERSON>', avatar: 'https://i.pravatar.cc/150?img=3' },
];

const MentionDropdown = ({ onClose }) => {
  const [search, setSearch] = useState('');

  const filtered = dummyUsers.filter(u => u.name.toLowerCase().includes(search.toLowerCase()));

  return (
    <View style={styles.dropdown}>
      <TextInput
        placeholder="Search user..."
        value={search}
        onChangeText={setSearch}
        style={styles.search}
      />
      <FlatList
        data={filtered}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <View style={styles.item}>
            <Image source={{ uri: item.avatar }} style={styles.avatar} />
            <Text style={styles.name}>{item.name}</Text>
          </View>
        )}
      />
    </View>
  );
};

export default MentionDropdown;

const styles = StyleSheet.create({
  dropdown: {
    position: 'absolute', bottom: 70, left: 10, right: 10,
    backgroundColor: '#fff', borderRadius: 10,
    padding: 10, elevation: 10,
  },
  search: {
    borderBottomWidth: 1, borderColor: '#ccc',
    paddingVertical: 4, marginBottom: 10,
  },
  item: {
    flexDirection: 'row', alignItems: 'center', paddingVertical: 8,
  },
  avatar: {
    width: 30, height: 30, borderRadius: 15, marginRight: 10,
  },
  name: {
    fontSize: 16,
  },
});
