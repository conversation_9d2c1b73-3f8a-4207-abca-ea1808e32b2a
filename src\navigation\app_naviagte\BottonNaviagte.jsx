// MainTabs.js
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  SafeAreaView,
  Platform,
} from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/Ionicons';
import LinearGradient from 'react-native-linear-gradient';
import ImagePicker from 'react-native-image-crop-picker';

import Dashboard from '../../screen/app/Dashboard';
import Profile from '../../screen/app/Profile';
import Inbox from '../../screen/app/Inbox';
import Friend from '../../screen/app/Friend';
import { Color } from '../../constant/color';

const { width } = Dimensions.get('window');
const Tab = createBottomTabNavigator();

const CustomTabBar = ({ state, descriptors, navigation }) => {
  const [activeTab, setActiveTab] = useState(0);
  const circlePosition = useRef(new Animated.Value(0)).current;
  const circleScale = useRef(new Animated.Value(1)).current;

  const tabAnimations = useRef(
    state.routes.map(() => ({
      scale: new Animated.Value(1),
      opacity: new Animated.Value(0.6),
    }))
  ).current;

  const centerButtonAnim = useRef({
    scale: new Animated.Value(1),
    rotate: new Animated.Value(0),
  }).current;

  useEffect(() => {
    const tabWidth = width / 5;
    const targetPosition = state.index * tabWidth;

    Animated.parallel([
      Animated.spring(circlePosition, {
        toValue: targetPosition,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.sequence([
        Animated.spring(circleScale, {
          toValue: 1.15,
          friction: 6,
          useNativeDriver: true,
        }),
        Animated.spring(circleScale, {
          toValue: 1,
          friction: 6,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    tabAnimations.forEach((anim, index) => {
      const isActive = state.index === index;
      Animated.parallel([
        Animated.spring(anim.scale, {
          toValue: isActive ? 1.1 : 1,
          friction: 6,
          tension: 100,
          useNativeDriver: true,
        }),
        Animated.timing(anim.opacity, {
          toValue: isActive ? 1 : 0.6,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    });

    setActiveTab(state.index);
  }, [state.index]);

  const openPicker = useCallback(() => {
    Animated.sequence([
      Animated.parallel([
        Animated.spring(centerButtonAnim.scale, {
          toValue: 0.9,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.timing(centerButtonAnim.rotate, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.spring(centerButtonAnim.scale, {
          toValue: 1,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.timing(centerButtonAnim.rotate, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    ImagePicker.openPicker({ mediaType: 'any', multiple: false })
      .then(media => {
        if (media) {
          navigation.navigate('Upload', { media });
        }
      })
      .catch(e => console.log('Picker cancelled', e));
  }, [navigation]);

  const getTabConfig = (routeName) => {
    const configs = {
      Dashboard: { icon: 'home', activeIcon: 'home', label: 'Home' },
      Friend: { icon: 'people', activeIcon: 'people', label: 'Friends' },
      Inbox: { icon: 'mail', activeIcon: 'mail', label: 'Messages' },
      Profile: { icon: 'person', activeIcon: 'person', label: 'Profile' },
    };
    return configs[routeName] || configs.Dashboard;
  };

  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.darkContainer}>
        {/* Moving Green Gradient Circle */}
        <Animated.View
          style={[
            styles.movingGreenCircle,
            {
              transform: [
                { translateX: circlePosition },
                { scale: circleScale },
                { scaleY: 1.05 },
              ],
            },
          ]}
        >
          <LinearGradient
            colors={[Color.purple, Color.pink]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientCircle}
          >
            <Icon
              name={getTabConfig(state.routes[state.index].name).activeIcon}
              size={18}
              color="#fff"
            />
          </LinearGradient>
        </Animated.View>

        {/* Tab Icons */}
        <View style={styles.tabItemsContainer}>
          {state.routes.map((route, index) => {
            const tabConfig = getTabConfig(route.name);
            const isFocused = state.index === index;
            const animation = tabAnimations[index];

            const onPress = () => {
              const event = navigation.emit({
                type: 'tabPress',
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate(route.name);
              }
            };

            return (
              <TouchableOpacity
                key={route.key}
                onPress={onPress}
                style={styles.tabItem}
                activeOpacity={0.9}
              >
                <Animated.View
                  style={[
                    styles.tabIconContainer,
                    {
                      opacity: isFocused ? 0 : animation.opacity,
                      transform: [{ scale: animation.scale }],
                    },
                  ]}
                >
                  <Icon
                    name={tabConfig.icon}
                    size={16}
                    color="rgba(255,255,255,0.8)"
                  />
                </Animated.View>
              </TouchableOpacity>
            );
          })}

          {/* Upload Tab */}
          <TouchableOpacity
            onPress={openPicker}
            style={styles.tabItem}
            activeOpacity={0.9}
          >
            <Animated.View
              style={[
                styles.tabIconContainer,
                {
                  opacity: state.index === 4 ? 0 : 0.6,
                  transform: [
                    { scale: centerButtonAnim.scale },
                    {
                      rotate: centerButtonAnim.rotate.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '45deg'],
                      }),
                    },
                  ],
                },
              ]}
            >
              <Icon name="add" size={16} color="rgba(255,255,255,0.8)" />
            </Animated.View>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const MainTabs = () => {
  return (
    <Tab.Navigator
      tabBar={(props) => <CustomTabBar {...props} />}
      screenOptions={{ headerShown: false }}
    >
      <Tab.Screen name="Dashboard" component={Dashboard} />
      <Tab.Screen name="Friend" component={Friend} />
      <Tab.Screen name="Inbox" component={Inbox} />
      <Tab.Screen name="Profile" component={Profile} />
    </Tab.Navigator>
  );
};

export default MainTabs;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Color.Primary,
  },
  tabBarContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    justifyContent: 'center',
  },
  darkContainer: {
    height: 50,
    backgroundColor: Color.Main,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    position: 'relative',
    overflow: 'visible',
  },
  movingGreenCircle: {
    position: 'absolute',
    width: 60,
    height: 50,
    borderRadius: 25,
    top: -20,
    elevation: 15,
    zIndex: 999,
    shadowColor: Color.pink,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.5,
    shadowRadius: 12,
    borderWidth: 7,
    borderColor: '#1a1a1a',
    overflow: 'hidden',
  },
  gradientCircle: {
    flex: 1,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabItemsContainer: {
    flexDirection: 'row',
    height: '100%',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    zIndex: 1,
  },
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
