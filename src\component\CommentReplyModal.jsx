import React, { useEffect, useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';
import { useDispatch, useSelector } from 'react-redux';
import { getPostComments, addCommentToPost } from '../redux/action/PostAction';
import CustomToast from './CustomToast';

// Get dimensions outside of component to avoid 'in' operator issues
const { width, height } = Dimensions.get('window');

const CommentModal = ({ visible, onClose, postId }) => {
  const dispatch = useDispatch();
  // Ensure state.posts exists before destructuring to avoid 'in' operator issues
  const posts = useSelector(state => state.posts);
  const { comments = [], loading = false } = posts || {};
  const [comment, setComment] = useState('');
  const [toastData, setToastData] = useState(null);

  useEffect(() => {
    if (visible && postId) {
      dispatch(getPostComments(postId));
    }
  }, [visible, postId, dispatch]);

  const handleSend = async () => {
    if (comment.trim() === '') return;
    try {
      const result = await dispatch(addCommentToPost(postId, comment));
      if (result.success) {
        setToastData({ type: 'success', message: 'Comment added!' });
        setComment('');
        dispatch(getPostComments(postId));
      } else {
        setToastData({ type: 'error', message: result.error || 'Failed to add comment' });
      }
    } catch (err) {
      setToastData({ type: 'error', message: 'Failed to add comment' });
    }
  };

  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.commentCount}>{comments?.length || 0} comments</Text>
            <TouchableOpacity onPress={onClose}>
              <Icon name="close" size={24} color={Color.Primary} />
            </TouchableOpacity>
          </View>
          <FlatList
            data={comments || []}
            keyExtractor={item => {
              // Safely handle item properties
              const id = item && (item._id || item.id);
              return id ? id.toString() : Math.random().toString();
            }}
            renderItem={({ item }) => {
              // Ensure item is an object before accessing properties
              if (!item) return null;
              
              const avatar = item.user && item.user.profileImage ? item.user.profileImage : 
                           item.avatar ? item.avatar : 'https://i.pravatar.cc/150?img=10';
              
              const name = item.user && item.user.name ? item.user.name : 
                         item.name ? item.name : 'Anonymous';
              
              return (
                <View style={styles.commentItem}>
                  <Image style={styles.avatarPlaceholder} source={{ uri: avatar }} />
                  <View style={{ flex: 1 }}>
                    <Text style={styles.name}>{name}</Text>
                    <Text style={styles.commentText}>{item.text || ''}</Text>
                  </View>
                </View>
              );
            }}
            style={styles.commentList}
            refreshing={loading}
            onRefresh={() => dispatch(getPostComments(postId))}
          />
          <View style={styles.inputRow}>
            <TextInput
              style={styles.input}
              placeholder="Add a comment..."
              value={comment}
              onChangeText={setComment}
            />
            <TouchableOpacity onPress={handleSend} style={styles.sendCommentButton}>
              <Icon name="send" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={() => setToastData(null)}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    paddingBottom: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomColor: '#eee',
    borderBottomWidth: 1,
  },
  commentCount: {
    fontFamily: fontFamilies.POPPINS.bold,
    fontSize: 17,
    color: Color.Primary,
  },
  commentItem: {
    flexDirection: 'row',
    padding: 12,
  },
  avatarPlaceholder: {
    width: 35,
    height: 35,
    borderRadius: 20,
    backgroundColor: '#aaa',
    marginRight: 10,
  },
  name: {
    fontFamily: fontFamilies.POPPINS.semiBold,
  },
  commentText: {
    marginVertical: 2,
    fontFamily: fontFamilies.POPPINS.regular,
  },
  reply: {
    color: Color.Primary,
    fontSize: 13,
    fontFamily: fontFamilies.POPPINS.medium,
  },
  replyInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 10,
  },
  replyContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 8,
    paddingLeft: 10,
    gap: 10,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    justifyContent: 'center',
    padding: 5,
  },
  input: {
    flex: 0.9,
    borderWidth: 0,
    paddingVertical: 11,
    paddingHorizontal: 10,
    backgroundColor: Color.inputBg,
    borderRadius: 30,
  },
  sendCommentButton: {
    backgroundColor: 'green',
    height: 30,
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40,
  },
  sendIcon: {
    marginLeft : 2
  }
});

export default CommentModal;
