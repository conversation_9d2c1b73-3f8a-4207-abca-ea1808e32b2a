const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // Basic Info
  name: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  phone: { type: String, required: true },
  address: { type: String, required: true },

  // Profile
  gender: { type: String, enum: ['male', 'female', 'other'], default: null, required: false },
  profileImage: { type: String, default: null },
  bio: { type: String, default: '' },

  // Verification
  isVerified: { type: Bo<PERSON>an, default: false },
  emailVerificationToken: { type: String, default: null },
  emailVerificationExpiry: { type: Date, default: null },

  // Password Reset
  resetPasswordOTP: { type: String, default: null },
  resetPasswordExpiry: { type: Date, default: null },

  // Activity
  lastLogin: { type: Date, default: null },
  isActive: { type: Boolean, default: true },

  // Social Stats (for your app)
  followers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  following: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  followersCount: { type: Number, default: 0 },
  followingCount: { type: Number, default: 0 },

  // App Specific
  videos: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Video' }],
  likes: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Video' }],

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Index for better performance
userSchema.index({ email: 1 });
userSchema.index({ phone: 1 });
userSchema.index({ createdAt: -1 });

module.exports = mongoose.model('User', userSchema);
