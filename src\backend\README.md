# Backend Server

This directory contains the backend server files for the MyPortfolio app.

## Files

- `simple-auth-server.js` - Main authentication server with mock endpoints
- `start-backend.bat` - Windows batch script to start the server
- Other server files for different configurations

## Running the Server

### Option 1: Using the batch script (Windows)
```bash
# From the project root directory
src\backend\start-backend.bat
```

### Option 2: Manual start
```bash
# Navigate to backend directory
cd src\backend

# Start the server
node simple-auth-server.js
```

The server will start on `http://***************:3001` by default.

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile (requires auth)
- `PUT /api/auth/profile` - Update user profile (requires auth)
- `POST /api/auth/complete-profile` - Complete user profile (requires auth)
- `POST /api/auth/change-password` - Change password (requires auth)
- `POST /api/auth/logout` - User logout (requires auth)

All authenticated endpoints require the `Authorization: Bearer <token>` header.
