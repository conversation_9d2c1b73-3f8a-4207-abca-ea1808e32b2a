import {createStore, applyMiddleware}  from "redux"
import { Reducer } from "../reducer";

// Create a simple thunk middleware
const thunk = (store) => (next) => (action) => {
  if (typeof action === 'function') {
    return action(store.dispatch, store.getState);
  }
  return next(action);
};

// Initial state to prevent undefined state issues
const initialState = {
  auth: {
    user: null,
    token: null,
    isAuth: false,
    loading: false,
    error: null,
  },
  posts: {
    posts: [],
    comments: [],
    loading: false,
    error: null
  },
  users: {
    searchResults: [],
    userProfile: null,
    followers: [],
    following: [],
    loading: false,
    error: null
  },
  chat: {
    messages: [],
    chatList: [],
    loading: false,
    error: null
  }
};

const store = createStore(
  Reducer,
  initialState,
  applyMiddleware(thunk)
);
export default store;