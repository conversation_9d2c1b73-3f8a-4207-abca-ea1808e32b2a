import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Login from '../../screen/Auth/Login';
import SignUp from '../../screen/Auth/SignUp';
import OTPVerification from '../../screen/Auth/OTPVerification';
import Forgot from '../../screen/Auth/Forgot';
import NewPassword from '../../screen/Auth/NewPassword';
import CompleteProfile from '../../screen/Auth/CompleteProfile';

const Stack = createStackNavigator();

export default function AuthNavigate() {
  return (
    <Stack.Navigator  initialRouteName="SignUp" screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="SignUp" component={SignUp} />
      <Stack.Screen name="OTPVerification" component={OTPVerification} />
      <Stack.Screen name="Forgot" component={Forgot} />
      <Stack.Screen name="NewPassword" component={NewPassword} />
      <Stack.Screen name="CompleteProfile" component={CompleteProfile} />

      
    </Stack.Navigator>
  );
}