{"name": "auth-api", "version": "1.0.0", "description": "Authentication API for MyPortfolio App", "main": "server.jsx", "scripts": {"start": "node server.jsx", "dev": "nodemon server.jsx", "test": "node ../test_api.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "nodemailer": "^6.9.4", "multer": "^1.4.5-lts.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1", "axios": "^1.5.0"}, "keywords": ["auth", "api", "express", "mongodb", "jwt"], "author": "<PERSON><PERSON>", "license": "MIT"}