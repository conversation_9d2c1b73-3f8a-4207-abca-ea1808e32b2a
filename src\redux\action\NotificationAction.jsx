import AsyncStorage from '@react-native-async-storage/async-storage';
import OneSignalConfig from '../../config/OneSignalConfig';

// API Base URL
const BASE_URL = 'http://192.168.100.150:3002/api';

// Action Types
export const SET_LOADING = 'SET_NOTIFICATION_LOADING';
export const SET_ERROR = 'SET_NOTIFICATION_ERROR';
export const CLEAR_ERROR = 'CLEAR_NOTIFICATION_ERROR';
export const SET_NOTIFICATIONS = 'SET_NOTIFICATIONS';
export const ADD_NOTIFICATION = 'ADD_NOTIFICATION';
export const MARK_NOTIFICATION_READ = 'MARK_NOTIFICATION_READ';
export const CLEAR_ALL_NOTIFICATIONS = 'CLEAR_ALL_NOTIFICATIONS';

// Action Creators
export const setLoading = (loading) => ({
  type: SET_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: SET_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

export const setNotifications = (notifications) => ({
  type: SET_NOTIFICATIONS,
  payload: notifications
});

export const addNotification = (notification) => ({
  type: ADD_NOTIFICATION,
  payload: notification
});

export const markNotificationRead = (notificationId) => ({
  type: MARK_NOTIFICATION_READ,
  payload: notificationId
});

export const clearAllNotifications = () => ({
  type: CLEAR_ALL_NOTIFICATIONS
});

// Get All Notifications
export const getNotifications = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      console.log('Retrieved token for getNotifications:', token);
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/notifications`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get notifications API response:', result);

      if (result.success) {
        dispatch(setNotifications(result.notifications));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, notifications: result.notifications };
    } catch (error) {
      console.error('Get notifications error:', error);
      dispatch(setError(error.message || 'Failed to get notifications'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Send Like Notification
export const sendLikeNotification = (postId, postOwnerId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');
      const user = userData ? JSON.parse(userData) : null;

      if (!token || !user) {
        throw new Error('No authentication data found');
      }

      // Create notification data
      const notificationData = {
        type: 'like',
        title: 'New Like',
        message: `${user.name} liked your post`,
        postId: postId,
        fromUserId: user.id,
        toUserId: postOwnerId,
        data: {
          type: 'like',
          postId: postId,
          userId: user.id
        }
      };

      // Send to server
      const response = await fetch(`${BASE_URL}/notifications/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(notificationData)
      });

      const result = await response.json();
      console.log('Send like notification response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Send like notification error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Send Follow Notification
export const sendFollowNotification = (followedUserId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');
      const user = userData ? JSON.parse(userData) : null;

      if (!token || !user) {
        throw new Error('No authentication data found');
      }

      const notificationData = {
        type: 'follow',
        title: 'New Follower',
        message: `${user.name} started following you`,
        fromUserId: user.id,
        toUserId: followedUserId,
        data: {
          type: 'follow',
          userId: user.id
        }
      };

      const response = await fetch(`${BASE_URL}/notifications/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(notificationData)
      });

      const result = await response.json();
      console.log('Send follow notification response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Send follow notification error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Send Comment Notification
export const sendCommentNotification = (postId, postOwnerId, commentText) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const userData = await AsyncStorage.getItem('userData');
      const user = userData ? JSON.parse(userData) : null;

      if (!token || !user) {
        throw new Error('No authentication data found');
      }

      const notificationData = {
        type: 'comment',
        title: 'New Comment',
        message: `${user.name} commented: ${commentText.substring(0, 50)}...`,
        postId: postId,
        fromUserId: user.id,
        toUserId: postOwnerId,
        data: {
          type: 'comment',
          postId: postId,
          userId: user.id,
          comment: commentText
        }
      };

      const response = await fetch(`${BASE_URL}/notifications/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(notificationData)
      });

      const result = await response.json();
      console.log('Send comment notification response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Send comment notification error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Send OTP Notification
export const sendOTPNotification = (phoneNumber, otp) => {
  return async (dispatch) => {
    try {
      const notificationData = {
        type: 'otp',
        title: 'OTP Verification',
        message: `Your OTP is: ${otp}`,
        phoneNumber: phoneNumber,
        data: {
          type: 'otp',
          otp: otp
        }
      };

      const response = await fetch(`${BASE_URL}/notifications/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notificationData)
      });

      const result = await response.json();
      console.log('Send OTP notification response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Send OTP notification error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Mark Notification as Read
export const markNotificationAsRead = (notificationId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Mark notification read response:', result);

      if (result.success) {
        dispatch(markNotificationRead(notificationId));
      }

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Mark notification read error:', error);
      return { success: false, error: error.message };
    }
  };
};
