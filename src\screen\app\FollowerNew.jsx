import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { getFollowers, followUser } from '../../redux/action/UserAction';
import CustomToast from '../../component/CustomToast';

// Fallback data
const fallbackFollowers = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    bio: 'Content Creator 🎬',
    profileImage: null,
    followersCount: 1250,
    isFollowing: false,
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    bio: 'Mehendi Artist 🎨',
    profileImage: null,
    followersCount: 890,
    isFollowing: true,
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    bio: 'Photographer 📸',
    profileImage: null,
    followersCount: 567,
    isFollowing: false,
  },
];

const FollowerNew = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  
  const { followers, loading } = useSelector(state => state.users || {});
  
  const [search, setSearch] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);
  
  const userId = route.params?.userId; // Get userId from navigation params

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  // Load followers on component mount
  useEffect(() => {
    if (userId) {
      loadFollowers();
    }
  }, [userId]);

  const loadFollowers = async () => {
    try {
      const result = await getFollowers(userId)(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load followers');
      }
    } catch (error) {
      console.error('Load followers error:', error);
      showToast('error', 'Failed to load followers');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFollowers();
    setRefreshing(false);
  };

  // Use API followers or fallback data
  const displayFollowers = followers && followers.length > 0 ? followers : fallbackFollowers;

  const filteredData = displayFollowers.filter(item =>
    item.name?.toLowerCase().includes(search.toLowerCase())
  );

  const handleFollowToggle = async (id, currentStatus) => {
    try {
      const result = await followUser(id)(dispatch);
      if (result.success) {
        showToast('success', result.isFollowing ? 'Following user' : 'Unfollowed user');
        // Refresh the followers list
        loadFollowers();
      } else {
        showToast('error', result.error || 'Failed to follow user');
      }
    } catch (error) {
      console.error('Follow user error:', error);
      showToast('error', 'Failed to follow user');
    }
  };

  const handleChatPress = (user) => {
    // Only allow chat with followers/following
    navigation.navigate('ChatScreen', {
      userId: user.id,
      userName: user.name,
      userImage: user.profileImage
    });
  };

  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <TouchableOpacity onPress={() => navigation.navigate('ViewInboxProfile', { userId: item.id })}>
        <Image
          source={
            item?.profileImage 
              ? { uri: `http://192.168.100.38:3001/uploads/profiles/${item.profileImage}` }
              : require('../../assets/image/music.jpg')
          }
          style={styles.profileImage}
        />
      </TouchableOpacity>
      
      <View style={styles.textContainer}>
        <Text style={styles.name}>{item?.name}</Text>
        <Text style={styles.bio}>{item?.bio || 'No bio available'}</Text>
        <Text style={styles.followersCount}>{item?.followersCount || 0} followers</Text>
      </View>
      
      <View style={styles.actionContainer}>
        {/* Chat Button - Only show for followers/following */}
        <TouchableOpacity 
          style={styles.chatButton}
          onPress={() => handleChatPress(item)}
        >
          <Icon name="chatbubble-outline" size={20} color="#fff" />
        </TouchableOpacity>
        
        {/* Follow Button */}
        <LinearGradient 
          colors={item.isFollowing ? ['#666', '#444'] : ['#ff00cc', '#333399']} 
          style={styles.followButton}
        >
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => handleFollowToggle(item.id, item.isFollowing)}
          >
            <Text style={styles.followButtonText}>
              {item.isFollowing ? 'Following' : 'Follow'}
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={26} color={Color.Secondary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Followers</Text>
        <View style={{ width: 26 }} />
      </View>

      {/* Search Box */}
      <View style={styles.searchBox}>
        <Icon name="search-outline" size={18} color="#888" />
        <TextInput
          style={styles.input}
          placeholder="Search followers..."
          placeholderTextColor="#aaa"
          value={search}
          onChangeText={setSearch}
        />
      </View>

      {/* Content */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.loadingText}>Loading followers...</Text>
        </View>
      ) : filteredData.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="people-outline" size={60} color="#666" />
          <Text style={styles.emptyText}>No followers found</Text>
          <Text style={styles.emptySubText}>
            {search ? 'Try searching with different keywords' : 'This user has no followers yet'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#fff"
            />
          }
        />
      )}

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Color.inputBg,
    marginHorizontal: 20,
    marginVertical: 15,
    paddingHorizontal: 15,
    borderRadius: 25,
    height: 45,
  },
  input: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.regular,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.05)',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  textContainer: {
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
    marginBottom: 2,
  },
  bio: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.regular,
    color: '#ccc',
    marginBottom: 2,
  },
  followersCount: {
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.regular,
    color: '#999',
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  chatButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Color.Primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  followButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  followButtonText: {
    color: '#fff',
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default FollowerNew;
