import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import LinearGradient from 'react-native-linear-gradient'
import { Image } from 'react-native-svg'
import { Color } from '../constant/color'
import { fontFamilies } from '../constant/Font'

const InboxCard = ({item}) => {
  console.log('item.image', item.image)
  const image = require('../assets/image/music.jpg')
  console.log('image', image)
  return (
    <>
    <View style={{flexDirection :'column', }}>
    <LinearGradient colors={['#ff00cc', '#333399']} style={styles.gradientBorder}>
           <View style={styles.imageContainer}>
             <Image
               source={image}
               style={styles.profileImage}
             />
           </View>
         </LinearGradient>
               <Text style={styles.username}> {item.name.length > 9 ? item.name.slice(0, 7) + '...' : item.name}</Text>
               </View>
      </>   
  )
}

export default InboxCard

const styles = StyleSheet.create({
     gradientBorder: {
    width: 70,
    height: 70,
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 49,
    backgroundColor: '#0D0D1F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 45,
  },
  username: {
    color: Color.Secondary,
    fontSize: 18,
    fontFamily : fontFamilies.POPPINS.regular,
    // fontWeight: '600',
    marginTop: 12,
  },
})