import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import ForyouVideoComponent from '../../component/ForyouVideoComponent';

const data = [
  {
    id: 1,
    tittle: 'Jhon',
    description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj',
    video: require('../../assets/video/video_2.mp4'),
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
  },
  {
    id: 2,
    tittle: 'Jhon',
    description:
      'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj djfrfmrej dkfgnrg fjgngn fbfbnfjnfnh fjgnjf dfkgnfnb dnfkvnfjk fngitghi ighit trihg hiti tgitih trhiti mthijtgjm tigjtih kghitj mtightr ',
    video: require('../../assets/video/video_1.mp4'),
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
  },
  {
    id: 3,
    tittle: 'Jhon',
    description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj',
    video: require('../../assets/video/video_3.mp4'),
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
  },
  {
    id: 4,
    tittle: 'Jhon',
    description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj',
    video: require('../../assets/video/video_2.mp4'),
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
  },
];

const UserPostDetails = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [loader, setLoader] = useState(false);

  const viewabilityConfig = useRef({
    viewAreaCoveragePercentThreshold: 50,
  }).current;

  const onViewRef = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setSelectedIndex(viewableItems[0].index);
    }
  });

  useEffect(() => {
    setLoader(true);
    const timer = setTimeout(() => {
      setLoader(false);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  const renderItem = ({ item, index }) => (
    <ForyouVideoComponent
      index={index}
      items={item}
      musicImage={item.musicImage}
      item={item.video}
      ProfileImage={item.ProfileImage}
      isPlay={index === selectedIndex}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      {loader ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      ) : (
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(_, index) => index.toString()}
          showsVerticalScrollIndicator={false}
          pagingEnabled
          onViewableItemsChanged={onViewRef.current}
          viewabilityConfig={viewabilityConfig}
        />
      )}
    </SafeAreaView>
  );
};

export default UserPostDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
});
