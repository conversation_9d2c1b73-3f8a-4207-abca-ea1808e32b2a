const express = require('express');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const cors = require('cors');
const connectDB = require('../config/db.js');

// Load .env file from the validators directory
dotenv.config({ path: path.join(__dirname, '.env') });

console.log('Environment loaded, PORT:', process.env.PORT);
console.log('MONGO_URI:', process.env.MONGO_URI ? 'Found' : 'Not found');

// Connect to MongoDB Atlas
console.log('Attempting to connect to MongoDB...');
connectDB()
  .then(() => {
    console.log('MongoDB connection successful!');
  })
  .catch(err => {
    console.log('MongoDB connection failed:', err.message);
    console.log('Server will continue without database connection');
  });

const app = express();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../../uploads');
const profilesDir = path.join(uploadsDir, 'profiles');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Created uploads directory');
}

if (!fs.existsSync(profilesDir)) {
  fs.mkdirSync(profilesDir, { recursive: true });
  console.log('Created profiles directory');
}

// Middleware
app.use(cors()); // Enable CORS for all routes
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (uploaded images)
app.use('/uploads', express.static(path.join(__dirname, '../../../uploads')));

// Basic test route
app.get('/', (req, res) => {
  res.json({
    message: 'Auth API Server is running!',
    endpoints: {
      register: 'POST /api/auth/register',
      login: 'POST /api/auth/login',
      forgotPassword: 'POST /api/auth/forgot-password',
      verifyOTP: 'POST /api/auth/verify-otp',
      resetPassword: 'POST /api/auth/reset-password',
      profile: 'GET /api/auth/profile (Protected)',
      updateProfile: 'PUT /api/auth/profile (Protected)',
      changePassword: 'POST /api/auth/change-password (Protected)',
      uploadImage: 'POST /api/auth/upload-profile-image (Protected)',
      logout: 'POST /api/auth/logout (Protected)',
      verifyToken: 'GET /api/auth/verify-token (Protected)'
    }
  });
});

// Auth routes
app.use('/api/auth', require('../routes/authRoting.js'));

const PORT = process.env.PORT || 3001;

console.log('Starting server...');
console.log('PORT:', PORT);

app.listen(PORT, () => {
  console.log(`🚀 Auth API Server running on port ${PORT}`);
  console.log(`📍 Server URL: http://localhost:${PORT}`);
  console.log(`📋 API Base URL: http://localhost:${PORT}/api/auth`);
  console.log('✅ Server started successfully!');
});
