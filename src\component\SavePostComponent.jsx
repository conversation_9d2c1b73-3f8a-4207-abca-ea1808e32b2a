import { FlatList, StyleSheet, View } from 'react-native';
import React from 'react';
import PostCard from './PostCard';

const PostData = [ 
  { id: 1, count: "40", mediaType: 'image', source: require("../assets/image/music.jpg") },
  { id: 2, count: "1000", mediaType: 'video', source: require("../assets/video/video_2.mp4")  },
  { id: 3, count: "2924", mediaType: 'image', source: require("../assets/image/music.jpg") },
  { id: 4, count: "3902", mediaType: 'video', source: require("../assets/video/video_2.mp4") },
  { id: 5, count: "9825", mediaType: 'image', source: require("../assets/image/music.jpg") },
  { id: 6, count: "3487", mediaType: 'image', source: require("../assets/image/music.jpg") },

];

const SavePostComponent = () => {
  return (
    <FlatList
      data={PostData}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => <PostCard item={item} />}
      numColumns={2}
      contentContainerStyle={styles.list}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default SavePostComponent;

const styles = StyleSheet.create({
  list: {
    padding: 10,
  },
});
