import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector, useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { checkAuthStatus } from '../../redux/action/AuthAction';
import MainTabs from './BottonNaviagte'; // your curved tab bar
import AuthNavigate from '../auth_naviagte/AuthNavigate';
import ChatScreen from '../../screen/app/ChatScreen';
import Upload from '../../screen/app/Upload';
import SettingScreen from '../../screen/app/SettingScreen';
import SearchScreen from '../../screen/app/SearchScreen';
import Following from '../../screen/app/Following';
import Follower from '../../screen/app/Follower';
import FollowerNew from '../../screen/app/FollowerNew';
import NotificationPage from '../../screen/app/NotificationPage';
import PrivateScreen from '../../screen/app/PrivateScreen';
import EditProfile from '../../screen/app/EditProfile';
import ChangePassward from '../../screen/app/ChangePassward';
import PolicyScreen from '../../screen/app/PolicyScreen';
import ViewInboxProfile from '../../screen/app/ViewInboxProfile';
import UserPostDetails from '../../screen/app/UserPostDetails';
import ReportScreen from '../../screen/app/ReportScreen';
import SuggestionsScreen from '../../screen/app/SuggestionsScreen';

const Stack = createStackNavigator();

export default function AppNavigator() {
  const dispatch = useDispatch();
  const { user, token, isAuth } = useSelector(state => state.auth || {});
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('Initializing auth state...', {
          reduxUser: user ? 'exists' : 'null',
          reduxToken: token ? 'exists' : 'null',
          isAuth
        });

        // If Redux already has auth data, use it
        if (isAuth && token && user) {
          console.log('User already authenticated via Redux');
          setIsLoggedIn(true);
          setIsLoading(false);
          return;
        }

        // Otherwise, check AsyncStorage and restore auth state
        console.log('Checking AsyncStorage for auth data...');
        const authResult = await dispatch(checkAuthStatus());
        
        console.log('Auth check result:', authResult);
        
        if (authResult.success) {
          setIsLoggedIn(true);
        } else {
          setIsLoggedIn(false);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing auth:', error);
        setIsLoggedIn(false);
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [dispatch]); // Remove dependencies to avoid infinite loops

  // Separate effect to handle Redux state changes
  useEffect(() => {
    if (isAuth && token && user) {
      console.log('Redux auth state updated - user is authenticated');
      setIsLoggedIn(true);
    } else if (isAuth === false) {
      console.log('Redux auth state updated - user is not authenticated');
      setIsLoggedIn(false);
    }
  }, [isAuth, token, user]);

  if (isLoading) {
    return null;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isLoggedIn ? (
          <>
            <Stack.Screen name="MainTabs" component={MainTabs} />
            <Stack.Screen name="ChatScreen" component={ChatScreen} />
            <Stack.Screen name="Upload" component={Upload} />
            <Stack.Screen name="SettingScreen" component={SettingScreen} />
            <Stack.Screen name="SearchScreen" component={SearchScreen} />
            <Stack.Screen name="Following" component={Following} />
            <Stack.Screen name="Follower" component={Follower} />
            <Stack.Screen name="FollowerNew" component={FollowerNew} />
            <Stack.Screen name="NotificationPage" component={NotificationPage} />
            <Stack.Screen name="PrivateScreen" component={PrivateScreen} />
            <Stack.Screen name="EditProfile" component={EditProfile} />
            <Stack.Screen name="ChangePassward" component={ChangePassward} />
            <Stack.Screen name="PolicyScreen" component={PolicyScreen} />
            <Stack.Screen name="ViewInboxProfile" component={ViewInboxProfile} />
            <Stack.Screen name="UserPostDetails" component={UserPostDetails} />
            <Stack.Screen name="ReportScreen" component={ReportScreen} />
            <Stack.Screen name="SuggestionsScreen" component={SuggestionsScreen} />

          </>
        ) : (
          <Stack.Screen name="AuthNavigate" component={AuthNavigate} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
