import AsyncStorage from '@react-native-async-storage/async-storage';

const BASE_URL = 'http://192.168.100.150:3002/api';

class ApiService {
  // Get auth headers with token
  static async getAuthHeaders() {
    const token = await AsyncStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Generic API request method
  static async makeRequest(endpoint, options = {}) {
    try {
      const url = `${BASE_URL}${endpoint}`;
      const headers = await this.getAuthHeaders();
      
      console.log(`Making ${options.method || 'GET'} request to:`, url);
      console.log('Headers:', headers);
      
      const config = {
        method: 'GET',
        headers,
        ...options
      };

      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('API Response:', result);
      
      return result;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // Auth-specific methods
  static async login(email, password) {
    return this.makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });
  }

  static async register(userData) {
    return this.makeRequest('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
  }

  static async getProfile() {
    return this.makeRequest('/auth/profile');
  }

  static async updateProfile(profileData) {
    return this.makeRequest('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData)
    });
  }

  static async completeProfile(profileData) {
    return this.makeRequest('/auth/complete-profile', {
      method: 'POST',
      body: JSON.stringify(profileData)
    });
  }

  static async changePassword(currentPassword, newPassword) {
    return this.makeRequest('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({ currentPassword, newPassword })
    });
  }

  static async logout() {
    return this.makeRequest('/auth/logout', {
      method: 'POST'
    });
  }

  // Generic POST request for other endpoints
  static async post(endpoint, data) {
    return this.makeRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // Generic GET request for other endpoints
  static async get(endpoint) {
    return this.makeRequest(endpoint);
  }

  // Generic PUT request for other endpoints
  static async put(endpoint, data) {
    return this.makeRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // Generic DELETE request for other endpoints
  static async delete(endpoint) {
    return this.makeRequest(endpoint, {
      method: 'DELETE'
    });
  }
}

export default ApiService;
