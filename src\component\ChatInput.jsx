import React, { useState } from 'react';
import {
  View, TextInput, TouchableOpacity,
  StyleSheet, Image, ScrollView, Text
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import EmojiPicker from 'rn-emoji-keyboard';
// AudioWaveform replaced with simple audio recording for APK build compatibility
// import WaveForm from '@alirehman7141/react-native-audiowaveform';
import { fontFamilies } from '../constant/Font';
import { Color } from '../constant/color';

const ChatInput = ({
  value, onChangeText, onSend,
  onStartRecording, onStopRecording,
  isRecording, isPaused, recordingDuration,
  onImagePress, selectedMedia, onRemoveMedia,
  isPreviewing, onPlayPreview, onPausePreview,
  onCancelPreview, recordedFilePath,
  finalRecordTime, isPlaying,
  replyingTo, onCancelReply,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasText = value.trim().length > 0;

  console.log('replyingTo', replyingTo?.media)
  

  return (
    <>
      {replyingTo && (
        <View style={styles.replyPreview}>
          <View style={styles.replyLine} />
          <View style={styles.replyContent}>
            <Text style={styles.replyLabel}>Replying to {replyingTo.sender ? 'yourself' : 'contact'}</Text>
            <Text style={styles.replyText} numberOfLines={1}>
              {replyingTo.text ||  <Image source={{ uri: replyingTo?.media[0]?.uri }} style={{ width: 20, height: 20 }} />}
            </Text>
          </View>
          <TouchableOpacity onPress={onCancelReply} style={styles.cancelReply}>
            <Icon name="close" size={20} color="#666" />
          </TouchableOpacity>
        </View>
      )}

      {selectedMedia?.length > 0 && (
        <ScrollView horizontal style={styles.mediaPreview}>
          {selectedMedia.map((it, i) => (
            <TouchableOpacity key={i} onPress={() => onRemoveMedia(i)}>
              <Image source={{ uri: it.path }} style={styles.previewImage} />
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}

      {isRecording ? (
        <View style={styles.recBar}>
          <Text style={styles.recLabel}>Recording</Text>
          <Text style={styles.recTime}>{recordingDuration}</Text>
          <TouchableOpacity onPress={onStopRecording} style={styles.stopBtn}>
            <Icon name="stop" size={18} color={Color.Secondary} />
          </TouchableOpacity>
        </View>
      ) : isPreviewing ? (
        <View style={styles.recBar}>
          <TouchableOpacity onPress={onCancelPreview}>
            <Icon name="delete" size={28} color="red" />
          </TouchableOpacity>

          <TouchableOpacity onPress={isPaused ? onPlayPreview : onPausePreview}>
            <Icon name={isPaused ? 'play-circle' : 'pause-circle'} size={28} color="#fff" />
          </TouchableOpacity>

          <WaveForm
            source={{ uri: recordedFilePath }}
            play={isPlaying}
            style={{ height: 20, width: '50%' }}
            waveFormStyle={{ waveColor: '#0db85c', scrubColor: '#fff' }}
          />

          <Text style={{ marginLeft: 10, color: '#ccc', flex: 1 }}>
            {finalRecordTime}
          </Text>

          <TouchableOpacity onPress={onSend}>
            <Icon name="send" size={24} color={Color.Primary} />
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.wrapper}>
          <TouchableOpacity onPress={() => setIsOpen(true)}>
            <Icon name="emoticon-outline" size={26} color="#bbb" />
          </TouchableOpacity>

          <TextInput
            style={styles.input}
            placeholder="Type a message"
            placeholderTextColor={Color.inputBg}
            value={value}
            onChangeText={onChangeText}
            multiline
          />

          <TouchableOpacity onPress={onImagePress}>
            <Icon name="image-outline" size={26} color="#bbb" />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={hasText || selectedMedia.length > 0 || isPreviewing ? onSend : onStartRecording}
            onLongPress={onStartRecording}
            delayLongPress={200}
          >
            <Icon
              name={hasText || selectedMedia.length > 0 || isPreviewing ? 'send' : 'microphone'}
              size={26}
              color="#0db85c"
            />
          </TouchableOpacity>
        </View>
      )}

      <EmojiPicker
        open={isOpen}
        onEmojiSelected={e => { onChangeText(value + e.emoji); setIsOpen(false); }}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#222',
    borderRadius: 30,
    margin: 10,
    padding: 8,
  },
  input: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    paddingHorizontal: 10,
    maxHeight: 120,
  },
  mediaPreview: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    marginVertical: 4,
  },
  previewImage: {
    width: 60,
    height: 60,
    marginRight: 8,
    borderRadius: 8,
  },
  recBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#444',
    borderRadius: 30,
    margin: 10,
    padding: 12,
    justifyContent: 'space-between',
    flexWrap: 'nowrap',
  },
  recTime: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.bold,
    fontSize: 15,
    marginHorizontal: 12,
  },
  recLabel: {
    color: Color.Secondary,
    fontFamily: fontFamilies.POPPINS.bold,
    fontSize: 15,
  },
  stopBtn: {
    backgroundColor: '#0db85c',
    width: 30,
    height: 30,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Reply preview styles
  replyPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#333',
    margin: 10,
    marginBottom: 0,
    padding: 12,
    borderRadius: 8,
  },
  replyLine: {
    width: 3,
    height: '100%',
    backgroundColor: '#25D366',
    borderRadius: 2,
    marginRight: 12,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: 12,
    color: '#25D366',
    fontWeight: 'bold',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 14,
    color: '#ccc',
  },
  cancelReply: {
    padding: 4,
  },
});

export default ChatInput;
