import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  SafeAreaView,
  StatusBar,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import Icon from 'react-native-vector-icons/Ionicons';
import inboxCard from '../../component/inboxCard';
import InboxCard from '../../component/inboxCard';
import LinearGradient from 'react-native-linear-gradient';
import InboxChatCard from '../../component/InboxChatCard';
import { useDispatch, useSelector } from 'react-redux';
import { getChatList } from '../../redux/action/ChatAction';
import { useNavigation } from '@react-navigation/native';
import CustomToast from '../../component/CustomToast';

const Inbox = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { chatList, loading } = useSelector(state => state.chat || {});

  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  // Static stories/active users section (replace with backend data if available)
  const storyData = [];

  // Load chat list on component mount
  useEffect(() => {
    loadChatList();
  }, []);

  const loadChatList = async () => {
    try {
      const result = await getChatList()(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load chats');
      }
    } catch (error) {
      console.error('Load chat list error:', error);
      showToast('error', 'Failed to load chats');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadChatList();
    setRefreshing(false);
  };

  // Only use backend chatList, no dummy data
  const displayChatData = chatList || [];

  const renderStoryItem = ({ item, index }) => (
    <InboxCard
      index={index}
      item={item}
    />
  );

  const renderChatItem = ({ item, index }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('ChatScreen', {
        userId: item.userId,
        userName: item.user?.name,
        userImage: item.user?.profileImage
      })}
    >
      <InboxChatCard
        index={index}
        item={{
          ...item,
          name: item.user?.name,
          image: item.user?.profileImage,
          lastMessage: item.lastMessage?.text,
          time: item.lastMessage?.createdAt,
          unreadCount: item.unreadCount
        }}
      />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#0D0D1F" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Inbox</Text>
        <TouchableOpacity onPress={() => navigation.navigate('SearchScreen')}>
          <Icon name="search" size={22} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Stories/Active Users Section (static, replace with backend if available) */}
      <View style={styles.storiesSection}>
        <Text style={styles.sectionTitle}>Active Now</Text>
        {storyData.length === 0 ? (
          <Text style={{ color: '#aaa', paddingHorizontal: 20 }}>
            (No active users. Connect backend to show real data.)
          </Text>
        ) : (
          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => item.id?.toString() || index.toString()}
            contentContainerStyle={styles.storiesList}
            data={storyData}
            renderItem={renderStoryItem}
          />
        )}
      </View>

      {/* Chat List Section */}
      <LinearGradient colors={['#ff00cc', '#333399']} style={styles.gradientBorder}>
        <View style={styles.chatContainer}>
          <Text style={styles.sectionTitle}>Messages</Text>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#fff" />
              <Text style={styles.loadingText}>Loading chats...</Text>
            </View>
          ) : displayChatData.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Icon name="chatbubbles-outline" size={60} color="#666" />
              <Text style={styles.emptyText}>No messages yet</Text>
              <Text style={styles.emptySubText}>Start a conversation with someone</Text>
            </View>
          ) : (
            <FlatList
              showsVerticalScrollIndicator={false}
              keyExtractor={(item, index) => item.id?.toString() || index.toString()}
              contentContainerStyle={styles.chatList}
              data={displayChatData}
              renderItem={renderChatItem}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  tintColor="#fff"
                />
              }
            />
          )}
        </View>
      </LinearGradient>

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default Inbox;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0D0D1F',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    color: '#fff',
    fontWeight: 'bold',
  },
  gradientBorder: {
    flex: 1,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40
  },
  storiesSection: {
    paddingVertical: 10,
  },
  sectionTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  storiesList: {
    gap: 20,
    paddingHorizontal: 20,
  },
  chatContainer: {
    flex: 1,
    backgroundColor: '#0D0D1F',
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    paddingTop: 20,
  },
  chatList: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },
});
