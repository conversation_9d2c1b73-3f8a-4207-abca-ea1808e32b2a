import OneSignal from 'react-native-onesignal';

// OneSignal App ID
const ONESIGNAL_APP_ID = '************************************';

class OneSignalConfig {
  static initialize() {
    console.log('🔔 Initializing OneSignal...');
    
    // Initialize OneSignal
    OneSignal.setAppId(ONESIGNAL_APP_ID);
    
    // Enable verbose logging for debugging
    OneSignal.setLogLevel(6, 0);
    
    // Request permission for notifications
    OneSignal.promptForPushNotificationsWithUserResponse(response => {
      console.log('🔔 OneSignal permission response:', response);
    });
    
    // Handle notification received
    OneSignal.setNotificationWillShowInForegroundHandler(notificationReceivedEvent => {
      console.log('🔔 Notification received in foreground:', notificationReceivedEvent);
      let notification = notificationReceivedEvent.getNotification();
      
      // Display the notification
      notificationReceivedEvent.complete(notification);
    });
    
    // Handle notification opened
    OneSignal.setNotificationOpenedHandler(notification => {
      console.log('🔔 Notification opened:', notification);
      
      // Handle different notification types
      const data = notification.notification.additionalData;
      if (data) {
        this.handleNotificationAction(data);
      }
    });
    
    console.log('✅ OneSignal initialized successfully');
  }
  
  static handleNotificationAction(data) {
    console.log('🔔 Handling notification action:', data);
    
    switch (data.type) {
      case 'otp':
        // Navigate to OTP screen
        console.log('📱 OTP notification received');
        break;
      case 'like':
        // Navigate to post or profile
        console.log('❤️ Like notification received');
        break;
      case 'follow':
        // Navigate to profile
        console.log('👥 Follow notification received');
        break;
      case 'comment':
        // Navigate to post
        console.log('💬 Comment notification received');
        break;
      case 'report':
        // Handle report notification
        console.log('⚠️ Report notification received');
        break;
      case 'save':
        // Navigate to saved posts
        console.log('💾 Save notification received');
        break;
      default:
        console.log('🔔 Unknown notification type');
    }
  }
  
  static async getUserId() {
    try {
      const deviceState = await OneSignal.getDeviceState();
      return deviceState.userId;
    } catch (error) {
      console.error('❌ Error getting OneSignal user ID:', error);
      return null;
    }
  }
  
  static async sendNotification(userId, title, message, data = {}) {
    try {
      console.log('🔔 Sending notification to user:', userId);
      
      const notificationObj = {
        app_id: ONESIGNAL_APP_ID,
        include_player_ids: [userId],
        headings: { en: title },
        contents: { en: message },
        data: data
      };
      
      // This would typically be done from your backend server
      console.log('🔔 Notification payload:', notificationObj);
      return true;
    } catch (error) {
      console.error('❌ Error sending notification:', error);
      return false;
    }
  }
}

export default OneSignalConfig;
