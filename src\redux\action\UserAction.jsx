import AsyncStorage from '@react-native-async-storage/async-storage';

// API Base URLapi
const BASE_URL = 'http://192.168.100.150:3002/api';

// Action Types
export const SET_LOADING = 'SET_USER_LOADING';
export const SET_ERROR = 'SET_USER_ERROR';
export const CLEAR_ERROR = 'CLEAR_USER_ERROR';
export const SET_SEARCH_RESULTS = 'SET_SEARCH_RESULTS';
export const SET_USER_PROFILE = 'SET_USER_PROFILE';
export const SET_FOLLOWERS = 'SET_FOLLOWERS';
export const SET_FOLLOWING = 'SET_FOLLOWING';

// Action Creators
export const setLoading = (loading) => ({
  type: SET_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: SET_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

export const setSearchResults = (users) => ({
  type: SET_SEARCH_RESULTS,
  payload: users
});

export const setUserProfile = (user) => ({
  type: SET_USER_PROFILE,
  payload: user
});

export const setFollowers = (followers) => ({
  type: SET_FOLLOWERS,
  payload: followers
});

export const setFollowing = (following) => ({
  type: SET_FOLLOWING,
  payload: following
});

// Search Users
export const searchUsers = (query) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/users/search?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Search users API response:', result);

      if (result.success) {
        dispatch(setSearchResults(result.users));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, users: result.users };
    } catch (error) {
      console.error('Search users error:', error);
      dispatch(setError(error.message || 'Failed to search users'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get User Profile
export const getUserProfile = (userId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get user profile API response:', result);

      if (result.success) {
        dispatch(setUserProfile(result.user));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, user: result.user, posts: result.posts };
    } catch (error) {
      console.error('Get user profile error:', error);
      dispatch(setError(error.message || 'Failed to get user profile'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Follow/Unfollow User
export const followUser = (userId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/users/${userId}/follow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Follow user API response:', result);

      return { success: result.success, data: result, isFollowing: result.isFollowing };
    } catch (error) {
      console.error('Follow user error:', error);
      return { success: false, error: error.message };
    }
  };
};

export const getFollowers = (userId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/users/${userId}/followers`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get followers API response:', result);

      if (result.success) {
        dispatch(setFollowers(result.followers));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, followers: result.followers };
    } catch (error) {
      console.error('Get followers error:', error);
      dispatch(setError(error.message || 'Failed to get followers'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Following
export const getFollowing = (userId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/users/${userId}/following`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get following API response:', result);

      if (result.success) {
        dispatch(setFollowing(result.following));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, following: result.following };
    } catch (error) {
      console.error('Get following error:', error);
      dispatch(setError(error.message || 'Failed to get following'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};
