import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, SafeAreaView, Modal, StatusBar } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/Ionicons';
import { fontFamilies } from '../../constant/Font';
import ProfileTapComponent from '../../component/ProfileTapComponent';
import ImagePicker from 'react-native-image-crop-picker';
import { useNavigation } from '@react-navigation/native';
import { Color } from '../../constant/color';
import LogoutButton from '../../component/LogoutButton';
import { useSelector, useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';


const ProfileCard = () => {
  const dispatch = useDispatch();
  const { user } = useSelector(state => state.auth || {});
  const { userProfile, followers, following, loading, error } = useSelector(state => state.user || {});
  const [image, setImage] = useState(null);
  const [likeModal, setLikeModal] = useState(false);
  const [toastData, setToastData] = useState(null);
  const navigation = useNavigation();

  const formatNumber = num => {
    if (!num) return '0';
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'm';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num?.toString();
  };

  // Load user profile and stats from backend
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        if (user && user._id) {
          const result = await dispatch(require('../../redux/action/UserAction').getUserProfile(user._id));
          if (!result.success) {
            setToastData({ type: 'error', message: result.error || 'Failed to load profile' });
          }
          await dispatch(require('../../redux/action/UserAction').getFollowers(user._id));
          await dispatch(require('../../redux/action/UserAction').getFollowing(user._id));
        }
      } catch (err) {
        setToastData({ type: 'error', message: 'Failed to load profile data' });
      }
    };
    fetchProfile();
  }, [user]);


  const pickImage = () => {
    ImagePicker.openPicker({
      width: 300,
      height: 300,
      cropping: true,
      cropperCircleOverlay: true,
      mediaType: 'photo',
    }).then(img => {
      setImage({ uri: img.path });
    }).catch(err => {
      console.log('User cancelled image selection');
    });
  };
  return (
    <>
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#0D0D1F" barStyle="light-content"  />
    {/* <View > */}
      <TouchableOpacity onPress={() => navigation.navigate('SettingScreen')} style={styles.settingIcon}>
        <Icon name="settings-outline" size={24} color="#fff" />
      </TouchableOpacity>

     <TouchableOpacity onPress={pickImage}>
      <LinearGradient colors={[Color.purple, Color.pink]} style={styles.gradientBorder}>
        <View style={styles.imageContainer}>
          <Image
            source={image ? image : require('../../assets/image/music.jpg')}
            style={styles.profileImage}
          />
        </View>
      </LinearGradient>
    </TouchableOpacity>


      <Text style={styles.username}>{userProfile?.name || userProfile?.username || user?.name || 'User'}</Text>

      <View style={styles.statsContainer}>
        <TouchableOpacity onPress={() => navigation.navigate('Following')} style={styles.statBox}>
          <Text style={styles.statValue}>{formatNumber(following?.length)}</Text>
          <Text style={styles.statLabel}>Following</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => navigation.navigate('Follower')} style={styles.statBox}>
          <Text style={styles.statValue}>{formatNumber(followers?.length)}</Text>
          <Text style={styles.statLabel}>Followers</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => setLikeModal(true)} style={styles.statBox}>
          <Text style={styles.statValue}>{formatNumber(userProfile?.likesCount)}</Text>
          <Text style={styles.statLabel}>Likes</Text>
        </TouchableOpacity>
      </View>

      {/* Logout Button */}
      <View style={{ marginTop: 20, paddingHorizontal: 20, width: '100%' }}>
        <LogoutButton
          style={{ backgroundColor: '#ff4444' }}
          textStyle={{ fontSize: 16 }}
        />
      </View>

     <ProfileTapComponent/>

    {/* </View> */}
    </SafeAreaView>
<Modal
  animationType="fade"
  transparent={true}
  visible={likeModal}
  onRequestClose={() => setLikeModal(false)}
>
  <View style={styles.overlay}>
    <View style={styles.modalBox}>
      <Text style={{ fontSize: 16,fontFamily: fontFamilies.POPPINS.extraBold,   marginBottom: 10 }}>Total Likes</Text>
      <Text style={{ fontSize: 14, fontFamily: fontFamilies.POPPINS.regular, marginBottom: 20, color: '#444' }}>
        You have received <Text style={{ fontWeight: 'bold' }}>{formatNumber(userProfile?.likesCount)}</Text> likes on your videos!
      </Text>
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={() => setToastData(null)}
        />
      )}

 <LinearGradient colors={['#ff00cc', '#333399']}  style={{
          backgroundColor: Color.Primary,
          paddingVertical: 10,
          borderRadius: 8,
          alignItems: 'center',
        }}>
      <TouchableOpacity
        onPress={() => setLikeModal(false)}
      >
        <Text style={{ color: '#fff', fontWeight: 'bold' }}>Close</Text>
      </TouchableOpacity>
      </LinearGradient>
    </View>
  </View>
</Modal>

    </>
  );
};

export default ProfileCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#0D0D1F',
    paddingVertical: 40,
    alignItems: 'center',
    flex: 1
  },
  settingIcon: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  gradientBorder: {
    width: 110,
    height: 110,
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: 98,
    height: 98,
    borderRadius: 49,
    backgroundColor: '#0D0D1F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 45,
  },
  username: {
    color: '#fff',
    fontSize: 20,
    fontWeight: '600',
    marginTop: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 25,
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 20,
  },
  statBox: {
    alignItems: 'center',
  },
  statValue: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  statLabel: {
    color: Color.inputBg,
    fontSize: 13,
    marginTop: 4,
    fontFamily: fontFamilies.POPPINS.medium
  },
  overlay: {
    flex: 1,
    backgroundColor: '#00000080',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBox: {
    backgroundColor: 'white',
    width:  '80%',
    borderRadius: 14,
    padding: 20,
    elevation: 5,
  },
});
