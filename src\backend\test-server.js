console.log('Starting test server...');

const express = require('express');
const cors = require('cors');

const app = express();

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  console.log('Root route accessed');
  res.json({ message: 'Test server working!', time: new Date() });
});

app.post('/api/auth/login', (req, res) => {
  console.log('Login route accessed:', req.body);
  res.json({
    success: true,
    message: 'Login successful',
    token: 'test-token-123',
    user: { id: 1, name: 'Test User', email: req.body.email }
  });
});

const PORT = 3001;

app.listen(PORT, () => {
  console.log(`Test server running on http://localhost:${PORT}`);
});
