{"name": "myportfolio-backend", "version": "1.0.0", "description": "Backend server for MyPortfolio app", "main": "simple-auth-server.js", "scripts": {"start": "node simple-auth-server.js", "dev": "nodemon simple-auth-server.js", "complete": "node complete-tiktok-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["express", "api", "backend", "auth"], "author": "<PERSON><PERSON>", "license": "MIT"}