import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Dimensions,
  ActivityIndicator,
  Text,
} from 'react-native';
import ForyouVideoComponent from './ForyouVideoComponent';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Sample data for following users' reels
const followingReelsData = [
  {
    id: 1,
    tittle: '<PERSON>',
    description: 'Amazing sunset from my balcony! #sunset #photography #golden #hour #peaceful',
    media: require('../assets/video/video_1.mp4'),
    mediaType: 'video',
    musicImage: require('../assets/image/music.jpg'),
    ProfileImage: require('../assets/image/music.jpg'),
    isFollowing: true,
  },
  {
    id: 2,
    tittle: '<PERSON>',
    description: 'Cooking my favorite pasta recipe #cooking #pasta #italian #food #delicious #homemade',
    media: require('../assets/image/music.jpg'),
    mediaType: 'image',
    musicImage: require('../assets/image/music.jpg'),
    ProfileImage: require('../assets/image/music.jpg'),
    isFollowing: true,
  },
  {
    id: 3,
    tittle: '<PERSON>',
    description: 'Dance practice session! Learning new moves #dance #practice #moves #fun #energy',
    media: require('../assets/video/video_2.mp4'),
    mediaType: 'video',
    musicImage: require('../assets/image/music.jpg'),
    ProfileImage: require('../assets/image/music.jpg'),
    isFollowing: true,
  },
  {
    id: 4,
    tittle: 'Alex Rodriguez',
    description: 'Street art discovery in downtown #streetart #art #urban #creative #colorful #inspiration',
    media: require('../assets/image/music.jpg'),
    mediaType: 'image',
    musicImage: require('../assets/image/music.jpg'),
    ProfileImage: require('../assets/image/music.jpg'),
    isFollowing: true,
  },
  {
    id: 5,
    tittle: 'Lisa Park',
    description: 'Morning workout routine #fitness #workout #morning #healthy #motivation #strong',
    media: require('../assets/video/video_3.mp4'),
    mediaType: 'video',
    musicImage: require('../assets/image/music.jpg'),
    ProfileImage: require('../assets/image/music.jpg'),
    isFollowing: true,
  },
];

const FollowingReelsComponent = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [reelsData, setReelsData] = useState(followingReelsData);

  const viewabilityConfig = useRef({
    viewAreaCoveragePercentThreshold: 50,
  }).current;

  const onViewRef = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setSelectedIndex(viewableItems[0].index);
    }
  });

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  const handleDeleteReel = (reelId) => {
    setReelsData(prevData => prevData.filter(reel => reel.id !== reelId));
  };

  const renderItem = ({ item, index }) => (
    <ForyouVideoComponent
      index={index}
      items={item}
      musicImage={item.musicImage}
      item={item.media}
      mediaType={item.mediaType}
      ProfileImage={item.ProfileImage}
      isPlay={index === selectedIndex}
      onDeleteVideo={handleDeleteReel}
    />
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  if (reelsData.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No reels from following users</Text>
        <Text style={styles.emptySubText}>
          Follow more users to see their reels here
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={reelsData}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
        pagingEnabled
        onViewableItemsChanged={onViewRef.current}
        viewabilityConfig={viewabilityConfig}
        style={styles.flatList}
        getItemLayout={(_, index) => ({
          length: screenHeight,
          offset: screenHeight * index,
          index,
        })}
        snapToInterval={screenHeight}
        snapToAlignment="start"
        decelerationRate="fast"
        removeClippedSubviews={true}
        maxToRenderPerBatch={2}
        windowSize={3}
        initialNumToRender={1}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    paddingHorizontal: 40,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubText: {
    color: '#888',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  flatList: {
    flex: 1,
    height: screenHeight,
  },
});

export default FollowingReelsComponent;
