import {
  SET_LOADING,
  SET_ERROR,
  CLEAR_ERROR,
  SET_CHAT_LIST,
  SET_MESSAGES,
  ADD_MESSAGE,
  SET_NOTIFICATIONS,
  ADD_NOTIFICATION
} from '../action/ChatAction';

const initialState = {
  chatList: [],
  messages: [],
  notifications: [],
  loading: false,
  error: null
};

const ChatReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case SET_CHAT_LIST:
      return {
        ...state,
        chatList: action.payload,
        loading: false,
        error: null
      };

    case SET_MESSAGES:
      return {
        ...state,
        messages: action.payload,
        loading: false,
        error: null
      };

    case ADD_MESSAGE:
      return {
        ...state,
        messages: [...state.messages, action.payload],
        loading: false,
        error: null
      };

    case SET_NOTIFICATIONS:
      return {
        ...state,
        notifications: action.payload,
        loading: false,
        error: null
      };

    case ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [action.payload, ...state.notifications],
        loading: false,
        error: null
      };

    default:
      return state;
  }
};

export default ChatReducer;
