import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Platform,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Image,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-community/blur';
import Icon from 'react-native-vector-icons/Ionicons';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { getNotifications, markNotificationRead, markAllNotificationsRead } from '../../redux/action/ChatAction';
import CustomToast from '../../component/CustomToast';



const NotificationPage = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { notifications, loading } = useSelector(state => state.chat || {});

  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  // Load notifications on component mount
  useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      const result = await getNotifications()(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load notifications');
      }
    } catch (error) {
      console.error('Load notifications error:', error);
      showToast('error', 'Failed to load notifications');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const handleMarkAllRead = async () => {
    try {
      const result = await markAllNotificationsRead()(dispatch);
      if (result.success) {
        showToast('success', 'All notifications marked as read');
        loadNotifications(); // Refresh the list
      } else {
        showToast('error', 'Failed to mark notifications as read');
      }
    } catch (error) {
      console.error('Mark all read error:', error);
      showToast('error', 'Failed to mark notifications as read');
    }
  };

  const handleNotificationPress = async (notification) => {
    // Mark as read if not already read
    if (!notification.isRead) {
      try {
        await markNotificationRead(notification.id)(dispatch);
      } catch (error) {
        console.error('Mark notification read error:', error);
      }
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'like':
      case 'comment':
        if (notification.postId || notification.post?.id) {
          navigation.navigate('UserPostDetails', {
            postId: notification.postId || notification.post.id
          });
        }
        break;
      case 'follow':
        if (notification.fromUserId || notification.fromUser?.id) {
          navigation.navigate('ViewInboxProfile', {
            userId: notification.fromUserId || notification.fromUser.id
          });
        }
        break;
      case 'message':
        if (notification.fromUserId || notification.fromUser?.id) {
          navigation.navigate('ChatScreen', {
            userId: notification.fromUserId || notification.fromUser.id,
            userName: notification.fromUser?.name,
            userImage: notification.fromUser?.profileImage
          });
        }
        break;
      default:
        break;
    }
  };

  // Only use backend notifications, no dummy data
  const displayNotifications = notifications || [];

  const getIconDetails = (type) => {
    switch (type) {
      case 'like':
        return { name: 'heart', color: '#FF4C4C' };
      case 'comment':
        return { name: 'chatbubble-ellipses', color: '#4CA1FF' };
      case 'follow':
        return { name: 'person-add', color: '#00C896' };
      case 'message':
        return { name: 'mail-open', color: '#FF9C00' };
      default:
        return { name: 'notifications', color: '#fff' };
    }
  };

  const formatTime = (dateString) => {
    const now = new Date();
    const notificationTime = new Date(dateString);
    const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hr ago`;
    return `${Math.floor(diffInMinutes / 1440)} day ago`;
  };

  const renderItem = ({ item }) => {
    const { name, color } = getIconDetails(item.type);
    return (
      <TouchableOpacity
        style={[styles.notificationWrapper, !item.isRead && styles.unreadNotification]}
        onPress={() => handleNotificationPress(item)}
      >
        {Platform.OS === 'ios' && (
          <BlurView style={styles.notificationCard} blurType="light" blurAmount={20} />
        )}
        <LinearGradient
          colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
          style={styles.notificationCard}
        >
          <View style={styles.avatarContainer}>
            {item.fromUser?.profileImage ? (
              <Image
                source={{ uri: `http://192.168.100.38:3001/uploads/profiles/${item.fromUser.profileImage}` }}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.defaultAvatar}>
                <Icon name="person" size={20} color="#fff" />
              </View>
            )}
            <View style={[styles.iconBadge, { backgroundColor: color }]}>
              <Icon name={name} size={12} color="#fff" />
            </View>
          </View>

          <View style={styles.contentContainer}>
            <Text style={styles.title}>{item.message}</Text>
            <Text style={styles.time}>{formatTime(item.createdAt)}</Text>
          </View>

          {!item.isRead && <View style={styles.unreadDot} />}
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  return (
    <LinearGradient colors={[Color.purple, '#ff00cc']} style={styles.container}>
      <SafeAreaView style={{ flex: 1 }}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
          <TouchableOpacity onPress={handleMarkAllRead}>
            <Text style={styles.markAllText}>Mark all read</Text>
          </TouchableOpacity>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Loading notifications...</Text>
          </View>
        ) : displayNotifications.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon name="notifications-outline" size={60} color="#fff" opacity={0.5} />
            <Text style={styles.emptyText}>No notifications yet</Text>
            <Text style={styles.emptySubText}>You'll see notifications here when someone interacts with your content</Text>
          </View>
        ) : (
          <FlatList
            data={displayNotifications}
            keyExtractor={(item) => item.id}
            renderItem={renderItem}
            contentContainerStyle={styles.list}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor="#fff"
              />
            }
          />
        )}

        {toastData && (
          <CustomToast
            type={toastData.type}
            message={toastData.message}
            onHide={hideToast}
          />
        )}
      </SafeAreaView>
    </LinearGradient>
  );
};

export default NotificationPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginTop: '12%',
    marginHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.extraBold,
    color: Color.Secondary,
    textAlign: 'center',
  },
  list: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    paddingTop: '8%',
  },
  notificationWrapper: {
    marginBottom: 15,
    overflow: 'hidden',
    borderRadius: 16,
  },
  notificationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  markAllText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubText: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  unreadNotification: {
    borderLeftWidth: 3,
    borderLeftColor: '#FF4C4C',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  defaultAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#666',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
  },
  contentContainer: {
    flex: 1,
  },
  title: {
    color: Color.Secondary,
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.bold,
    marginBottom: 4,
  },
  time: {
    color: Color.inputBg,
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.medium,
    opacity: 0.7,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF4C4C',
    marginLeft: 8,
  },
});
