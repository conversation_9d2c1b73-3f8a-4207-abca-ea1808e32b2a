# Authentication API Documentation

## Base URL
```
http://localhost:3001/api/auth
```

## Public Endpoints (No Authentication Required)

### 1. Register User
**POST** `/register`

**Request Body:**
```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "1234567890",
  "address": "123 Main St"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St",
    "isVerified": false
  }
}
```

### 2. Login User
**POST** `/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St",
    "isVerified": false,
    "profileImage": null
  }
}
```

### 3. Forgot Password
**POST** `/forgot-password`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP sent to your email successfully"
}
```

### 4. Verify OTP
**POST** `/verify-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP verified successfully"
}
```

### 5. Reset Password
**POST** `/reset-password`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456",
  "newPassword": "newpassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

## Protected Endpoints (Authentication Required)

**Headers Required:**
```
Authorization: Bearer your_jwt_token_here
```

### 6. Get User Profile
**GET** `/profile`

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St",
    "profileImage": null,
    "isVerified": false,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "lastLogin": "2024-01-01T00:00:00.000Z"
  }
}
```

### 7. Update Profile
**PUT** `/profile`

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "phone": "9876543210",
  "address": "456 Oak St"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "user": {
    "id": "user_id",
    "name": "John Smith",
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phone": "9876543210",
    "address": "456 Oak St",
    "profileImage": null
  }
}
```

### 8. Change Password
**POST** `/change-password`

**Request Body:**
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

### 9. Upload Profile Image
**POST** `/upload-profile-image`

**Request:** Multipart form data
- `profileImage`: Image file (max 5MB)

**Response:**
```json
{
  "success": true,
  "message": "Profile image uploaded successfully",
  "profileImage": "/uploads/profiles/profile-123456789.jpg"
}
```

### 10. Logout
**POST** `/logout`

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### 11. Verify Token
**GET** `/verify-token`

**Response:**
```json
{
  "success": true,
  "message": "Token is valid",
  "user": {
    "id": "user_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St",
    "profileImage": null,
    "isVerified": false
  }
}
```

## Error Responses

All endpoints return errors in this format:
```json
{
  "success": false,
  "message": "Error message here"
}
```

Common HTTP Status Codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `404`: Not Found
- `500`: Internal Server Error

## Environment Variables Required

```env
JWT_SECRET=your_jwt_secret_key
EMAIL_USER=your_gmail_address
EMAIL_PASS=your_gmail_app_password
MONGODB_URI=your_mongodb_connection_string
```
