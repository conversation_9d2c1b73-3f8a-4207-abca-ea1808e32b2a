import {
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
} from 'react-native';
import React from 'react';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import LinearGradient from 'react-native-linear-gradient';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';

const dummyImages = [
  require('../../assets/image/music.jpg'),
  require('../../assets/image/music.jpg'),
  require('../../assets/image/music.jpg'),
  require('../../assets/image/music.jpg'),
];

const ViewInboxProfile = () => {
  const navigation = useNavigation();

  const onBackPress = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBackPress} style={styles.iconButton}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>

        <TouchableOpacity onPress={() => navigation.navigate('ReportScreen')} style={styles.iconButton}>
          <Icon name="alert-circle-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Profile Section */}
      <View style={styles.profileSection}>
        <LinearGradient colors={[Color.purple, Color.pink]} style={styles.gradientBorder}>
          <View style={styles.imageContainer}>
            <Image source={require('../../assets/image/music.jpg')} style={styles.profileImage} />
          </View>
        </LinearGradient>
        <Text style={styles.username}>John Doe</Text>
      </View>

      {/* Divider */}
      <View style={styles.divider} />

      {/* Media */}
      <Text style={styles.mediaTitle}>Media</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaScroll}>
        {dummyImages.map((img, idx) => (
          <Image key={idx} source={img} style={styles.mediaImage} />
        ))}
      </ScrollView>

      {/* Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity style={[styles.button, { backgroundColor: '#FF3B30' }]}>
          <Text style={styles.buttonText}>Delete Chat</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, { backgroundColor: '#FF9500' }]}>
          <Text style={styles.buttonText}>Block</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default ViewInboxProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0D0D1F',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingTop: 15,
    alignItems: 'center',
  },
  iconButton: {
    padding: 8,
  },
  profileSection: {
    alignItems: 'center',
    marginTop: 10,
  },
  gradientBorder: {
    width: 110,
    height: 110,
    borderRadius: 55,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    width: 98,
    height: 98,
    borderRadius: 49,
    backgroundColor: '#0D0D1F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 45,
  },
  username: {
    color: Color.Secondary,
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.medium,
    marginTop: 10,
  },
  divider: {
    height: 1,
    backgroundColor: '#2E2E3D',
    marginVertical: 20,
    marginHorizontal: 20,
  },
  mediaTitle: {
    color: Color.Secondary,
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.medium,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  mediaScroll: {
    paddingLeft: 20,
  },
  mediaImage: {
    width: 90,
    height: 90,
    borderRadius: 8,
    marginRight: 12,
  },
  actionButtons: {
    marginTop: 30,
    paddingHorizontal: 20,
  },
  button: {
    paddingVertical: 14,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.medium,
  },
});
