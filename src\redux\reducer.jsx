import { combineReducers } from 'redux';
import {
  AUTH_LOADING,
  AUTH_SUCCESS,
  AUTH_ERROR,
  REGISTER_SUCCESS,
  LOGIN_SUCCESS,
  LOGOUT_SUCCESS,
  CLEAR_ERROR
} from './action/AuthAction';
import PostReducer from './reducer/PostReducer';
import UserReducer from './reducer/UserReducer';
import ChatReducer from './reducer/ChatReducer';

// Auth Reducer
const initialAuthState = {
  user: null,
  token: null,
  isAuth: false,
  loading: false,
  error: null,
};

const AuthReducer = (state = initialAuthState, action) => {
  switch (action.type) {
    case AUTH_LOADING:
      return {
        ...state,
        loading: action.payload,
        error: null,
      };
    case AUTH_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuth: true,
        loading: false,
        error: null,
      };
    case AUTH_ERROR:
      return {
        ...state,
        loading: false,
        error: action.payload,
      };
    case REGISTER_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuth: true,
        loading: false,
        error: null,
      };
    case LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuth: true,
        loading: false,
        error: null,
      };
    case LOGOUT_SUCCESS:
      return {
        ...state,
        user: null,
        token: null,
        isAuth: false,
        loading: false,
        error: null,
      };
    case CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Combine all reducers
export const Reducer = combineReducers({
  auth: AuthReducer,
  posts: PostReducer,
  users: UserReducer,
  chat: ChatReducer
});
