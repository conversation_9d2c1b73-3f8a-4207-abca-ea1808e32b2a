import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';
import { searchUsers, followUser } from '../../redux/action/UserAction';
import CustomToast from '../../component/CustomToast';

const SuggestionsScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  
  const { searchResults, loading } = useSelector(state => state.users || {});
  const { user: currentUser } = useSelector(state => state.auth || {});
  
  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);
  const [followingUsers, setFollowingUsers] = useState(new Set());

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  // Load suggested users on component mount
  useEffect(() => {
    loadSuggestedUsers();
  }, []);

  const loadSuggestedUsers = async () => {
    try {
      // Search for all users to show as suggestions
      const result = await searchUsers('')(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load suggestions');
      }
    } catch (error) {
      console.error('Load suggestions error:', error);
      showToast('error', 'Failed to load suggestions');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSuggestedUsers();
    setRefreshing(false);
  };

  const handleFollowUser = async (userId, userName) => {
    try {
      const result = await followUser(userId)(dispatch);
      if (result.success) {
        setFollowingUsers(prev => new Set([...prev, userId]));
        showToast('success', `Started following ${userName}`);
        // Refresh the list to update follow status
        setTimeout(() => {
          loadSuggestedUsers();
        }, 1000);
      } else {
        showToast('error', result.error || 'Failed to follow user');
      }
    } catch (error) {
      console.error('Follow user error:', error);
      showToast('error', 'Failed to follow user');
    }
  };

  const confirmFollow = (userId, userName) => {
    Alert.alert(
      'Follow User',
      `Do you want to follow ${userName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Follow', onPress: () => handleFollowUser(userId, userName) }
      ]
    );
  };

  const renderSuggestionItem = ({ item }) => {
    // Don't show current user in suggestions
    if (item.id === currentUser?.id) return null;
    
    const isFollowing = followingUsers.has(item.id);
    
    return (
      <View style={styles.suggestionItem}>
        <TouchableOpacity 
          style={styles.userInfo}
          onPress={() => navigation.navigate('ViewInboxProfile', { userId: item.id })}
        >
          <Image
            source={
              item.profileImage 
                ? { uri: `http://192.168.100.38:3001/uploads/profiles/${item.profileImage}` }
                : require('../../assets/image/music.jpg')
            }
            style={styles.profileImage}
          />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{item.name}</Text>
            <Text style={styles.userBio}>{item.bio || 'TikTok User'}</Text>
            <View style={styles.statsContainer}>
              <Text style={styles.statsText}>
                {item.followersCount || 0} followers • {item.postsCount || 0} posts
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.followButton,
            isFollowing && styles.followingButton
          ]}
          onPress={() => confirmFollow(item.id, item.name)}
          disabled={isFollowing}
        >
          <Text style={[
            styles.followButtonText,
            isFollowing && styles.followingButtonText
          ]}>
            {isFollowing ? 'Following' : 'Follow'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const filteredSuggestions = searchResults?.filter(user => user.id !== currentUser?.id) || [];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#0D0D1F" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Discover People</Text>
        <TouchableOpacity onPress={onRefresh}>
          <Icon name="refresh" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Suggested for you</Text>
        <Text style={styles.sectionSubtitle}>
          Based on your activity and who you might know
        </Text>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Color.Primary} />
            <Text style={styles.loadingText}>Finding people for you...</Text>
          </View>
        ) : filteredSuggestions.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Icon name="people-outline" size={60} color="#666" />
            <Text style={styles.emptyText}>No suggestions available</Text>
            <Text style={styles.emptySubText}>Check back later for new people to follow</Text>
          </View>
        ) : (
          <FlatList
            data={filteredSuggestions}
            renderItem={renderSuggestionItem}
            keyExtractor={(item) => item.id?.toString()}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor="#fff"
                colors={[Color.Primary]}
              />
            }
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>

      {/* Toast */}
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0D0D1F',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: fontFamilies.bold,
    color: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.bold,
    color: '#fff',
    marginTop: 20,
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: fontFamilies.regular,
    color: '#999',
    marginBottom: 20,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontFamily: fontFamilies.bold,
    color: '#fff',
    marginBottom: 2,
  },
  userBio: {
    fontSize: 14,
    fontFamily: fontFamilies.regular,
    color: '#999',
    marginBottom: 4,
  },
  statsContainer: {
    flexDirection: 'row',
  },
  statsText: {
    fontSize: 12,
    fontFamily: fontFamilies.regular,
    color: '#666',
  },
  followButton: {
    backgroundColor: Color.Primary,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  followingButton: {
    backgroundColor: '#333',
    borderWidth: 1,
    borderColor: '#555',
  },
  followButtonText: {
    fontSize: 14,
    fontFamily: fontFamilies.bold,
    color: '#fff',
  },
  followingButtonText: {
    color: '#999',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: fontFamilies.regular,
    color: '#999',
    marginTop: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontFamily: fontFamilies.bold,
    color: '#999',
    marginTop: 15,
  },
  emptySubText: {
    fontSize: 14,
    fontFamily: fontFamilies.regular,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 20,
  },
});

export default SuggestionsScreen;
