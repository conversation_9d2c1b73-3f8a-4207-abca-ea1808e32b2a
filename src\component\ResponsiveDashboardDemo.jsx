import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';
import Dashboard from '../screen/app/Dashboard';
import Following from '../screen/app/Following';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const ResponsiveDashboardDemo = () => {
  const [currentView, setCurrentView] = useState('dashboard');

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'following':
        return <Following />;
      default:
        return <Dashboard />;
    }
  };

  const features = [
    {
      title: '📱 Responsive Video/Image Feed',
      description: 'Dashboard now supports both videos and images with proper mobile responsive height',
      items: [
        'Full screen height for each item',
        'Smooth scrolling with snap-to-interval',
        'Support for both video and image content',
        'Optimized performance with proper item layout',
      ],
    },
    {
      title: '🎬 Mixed Media Support',
      description: 'Enhanced ForyouVideoComponent to handle both videos and images',
      items: [
        'Automatic media type detection',
        'Different interaction for images vs videos',
        'Proper resizing for different content types',
        'Consistent UI across media types',
      ],
    },
    {
      title: '👥 Following Reels Integration',
      description: 'Following screen now includes reels from followed users',
      items: [
        'Tab navigation between Followers and Reels',
        'Dedicated reels feed for following users',
        'Same responsive behavior as main dashboard',
        'Seamless switching between views',
      ],
    },
    {
      title: '⚡ Performance Optimizations',
      description: 'Improved performance for smooth scrolling experience',
      items: [
        'Proper getItemLayout for FlatList',
        'removeClippedSubviews for memory efficiency',
        'Optimized rendering with windowSize',
        'Snap-to-interval for precise scrolling',
      ],
    },
  ];

  if (currentView !== 'info') {
    return (
      <View style={styles.fullScreenContainer}>
        {renderCurrentView()}
        
        {/* Floating Info Button */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={() => setCurrentView('info')}
        >
          <Icon name="information-circle" size={24} color={Color.Secondary} />
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Enhanced Dashboard & Following</Text>
        <Text style={styles.subtitle}>
          Responsive video/image feed with Following reels integration
        </Text>
      </View>

      <View style={styles.navigationContainer}>
        <TouchableOpacity
          style={[styles.navButton, styles.dashboardButton]}
          onPress={() => setCurrentView('dashboard')}
        >
          <Icon name="home" size={20} color={Color.Secondary} />
          <Text style={styles.navButtonText}>Dashboard</Text>
          <Text style={styles.navButtonSubtext}>Mixed media feed</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navButton, styles.followingButton]}
          onPress={() => setCurrentView('following')}
        >
          <Icon name="people" size={20} color={Color.Secondary} />
          <Text style={styles.navButtonText}>Following</Text>
          <Text style={styles.navButtonSubtext}>Followers & Reels</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.featuresContainer} showsVerticalScrollIndicator={false}>
        <Text style={styles.featuresTitle}>✨ New Features</Text>
        
        {features.map((feature, index) => (
          <View key={index} style={styles.featureCard}>
            <Text style={styles.featureTitle}>{feature.title}</Text>
            <Text style={styles.featureDescription}>{feature.description}</Text>
            
            <View style={styles.featureItems}>
              {feature.items.map((item, itemIndex) => (
                <View key={itemIndex} style={styles.featureItem}>
                  <Icon name="checkmark-circle" size={16} color="#4CAF50" />
                  <Text style={styles.featureItemText}>{item}</Text>
                </View>
              ))}
            </View>
          </View>
        ))}

        <View style={styles.technicalDetails}>
          <Text style={styles.technicalTitle}>🔧 Technical Improvements</Text>
          
          <View style={styles.codeBlock}>
            <Text style={styles.codeTitle}>Responsive Height Implementation:</Text>
            <Text style={styles.codeText}>
              {`// Full screen height for each item
getItemLayout={(_, index) => ({
  length: screenHeight,
  offset: screenHeight * index,
  index,
})}

// Snap to interval for smooth scrolling
snapToInterval={screenHeight}
snapToAlignment="start"`}
            </Text>
          </View>

          <View style={styles.codeBlock}>
            <Text style={styles.codeTitle}>Mixed Media Support:</Text>
            <Text style={styles.codeText}>
              {`// Support for both images and videos
const renderMedia = () => {
  if (mediaType === 'image') {
    return <Image source={item} style={StyleSheet.absoluteFill} />;
  } else {
    return <Video source={item} style={StyleSheet.absoluteFill} />;
  }
};`}
            </Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            The dashboard now provides a fully responsive experience with support for both images and videos, 
            while the Following screen includes dedicated reels functionality.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
  },
  fullScreenContainer: {
    flex: 1,
    position: 'relative',
  },
  floatingButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  title: {
    fontSize: 24,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    textAlign: 'center',
    lineHeight: 22,
  },
  navigationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  navButton: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  dashboardButton: {
    backgroundColor: 'rgba(51, 51, 153, 0.2)',
  },
  followingButton: {
    backgroundColor: 'rgba(255, 0, 204, 0.2)',
  },
  navButtonText: {
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.semiBold,
    color: Color.Secondary,
    marginTop: 8,
  },
  navButtonSubtext: {
    fontSize: 12,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    marginTop: 4,
  },
  featuresContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  featuresTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
    marginBottom: 16,
  },
  featureCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.semiBold,
    color: Color.Secondary,
    marginBottom: 8,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    marginBottom: 12,
    lineHeight: 20,
  },
  featureItems: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureItemText: {
    fontSize: 13,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.Secondary,
    flex: 1,
    lineHeight: 18,
  },
  technicalDetails: {
    marginTop: 8,
  },
  technicalTitle: {
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    color: Color.Secondary,
    marginBottom: 16,
  },
  codeBlock: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  codeTitle: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.semiBold,
    color: Color.Secondary,
    marginBottom: 8,
  },
  codeText: {
    fontSize: 11,
    fontFamily: 'monospace',
    color: '#00FF88',
    lineHeight: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  footerText: {
    fontSize: 14,
    fontFamily: fontFamilies.POPPINS.regular,
    color: Color.gray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default ResponsiveDashboardDemo;
