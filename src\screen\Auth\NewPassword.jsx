import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import LinearGradient from 'react-native-linear-gradient';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation, useRoute } from '@react-navigation/native';
import CustomInput from '../../component/CustomInput';
import { useDispatch, useSelector } from 'react-redux';
import { resetPassword } from '../../redux/action/AuthAction';
import CustomToast from '../../component/CustomToast';

const NewPassword = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const { loading } = useSelector(state => state.auth || {});

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [toastData, setToastData] = useState(null);

  // Get email and OTP from route params
  const { email, otp } = route.params || {};

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const handleSubmit = async () => {
    setSubmitted(true);

    // Validation
    const passwordValid = newPassword.length >= 8 && /[^A-Za-z0-9]/.test(newPassword);
    const passwordsMatch = newPassword === confirmPassword;

    if (!newPassword || !confirmPassword) {
      showToast('error', 'Please fill all fields');
      return;
    }

    if (!passwordValid) {
      showToast('error', 'Password must be at least 8 characters with special characters');
      return;
    }

    if (!passwordsMatch) {
      showToast('error', 'Passwords do not match');
      return;
    }

    if (!email || !otp) {
      showToast('error', 'Missing email or OTP. Please go back and try again.');
      return;
    }

    try {
      console.log('Attempting to reset password:', { email, otp });

      // Dispatch reset password action
      const result = await resetPassword(email, otp, newPassword)(dispatch);

      console.log('Reset password result:', result);

      if (result.success) {
        showToast('success', 'Password reset successfully!');
        setTimeout(() => {
          navigation.navigate('Login');
        }, 1500);
      } else {
        showToast('error', result.message || 'Failed to reset password');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      showToast('error', 'Failed to reset password. Please try again.');
    }
  };
  return (
   <LinearGradient
        colors={['#333399', '#ff00cc']}
      style={styles.background}
      blurRadius={Platform.OS === 'android' ? 10 : 0}
    >
           <View style={styles.circleTopLeft} />
            <View style={styles.circleBottomRight} />
      <View style={styles.overlay}>
        {Platform.OS === 'ios' && (
          <BlurView style={styles.glass} blurType="light" blurAmount={20} />
        )}

        <LinearGradient
          colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
          style={styles.glass}
        >
          <Text style={styles.title}>New Password</Text>
         

<CustomInput
  label="New Password"
  placeholder="New password"
  value={newPassword}
  onChangeText={setNewPassword}
  iconLeft="lock-closed-outline"
  secureTextEntry
  iconRight="eye"
  error={submitted && !newPassword ? 'New Password required' : ''}
/>
       <CustomInput
  label="Confirm Password"
  placeholder="Confirm password"
  value={confirmPassword}
  onChangeText={setConfirmPassword}
  secureTextEntry
  iconLeft="lock-closed-outline"
  iconRight="eye"
  error={submitted && !confirmPassword ? 'Confirm Password required' : ''}
/>

          <TouchableOpacity
            onPress={handleSubmit}
            style={[styles.button, loading && styles.buttonDisabled]}
            disabled={loading}
          >
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#fff" />
                <Text style={[styles.buttonText, { marginLeft: 10 }]}>Resetting...</Text>
              </View>
            ) : (
              <Text style={styles.buttonText}>Reset Password</Text>
            )}
          </TouchableOpacity>

        </LinearGradient>
      </View>

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </LinearGradient>
  );
};

export default NewPassword;

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
  },
  overlay: {
    margin: 20,
    // borderRadius: 20,
    overflow: 'hidden',
  },
   circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  glass: {
    padding: 30,
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  title: {
    fontSize: 28,
    color: '#fff',
    marginBottom: 30,
    fontStyle: 'italic',
    fontFamily: fontFamilies.POPPINS.extraBold,
    textAlign: 'center',
  },
  input: {
    height: 45,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 15,
    color: '#fff',
    marginBottom: 15,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  forgot: {
    color: Color.Secondary,
    textAlign: 'center',
    marginTop: 15,
    fontFamily: fontFamilies.POPPINS.regular,
    textDecorationLine: 'underline',
  },
});
