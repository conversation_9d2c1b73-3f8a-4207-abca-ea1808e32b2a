import { View, Text, StyleSheet, Dimensions, Image, ScrollView, TouchableOpacity } from 'react-native';
import React from 'react';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/Ionicons'
import { Color } from '../constant/color';
import { useNavigation } from '@react-navigation/native';

const CARD_WIDTH = Dimensions.get('window').width / 2 - 20;

const PostCard = ({ item }) => {
const navigation = useNavigation();

   const formatNumber = num => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'm';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'k';
    return num?.toString();
  };
  
  return (
   <ScrollView>
  <TouchableOpacity onPress={() => navigation.navigate('UserPostDetails', { id: item.id })} style={styles.card}>
    {item.mediaType === 'image' ? (
      <Image source={typeof item.source === 'object' && item.source !== null ? item.source : { uri: typeof item.source === 'string' && item.source.trim() !== '' ? item.source : 'https://via.placeholder.com/300x200?text=No+Image' }} style={styles.media} />
    ) : (
      <Video
        source={typeof item.source === 'object' && item.source !== null ? item.source : { uri: typeof item.source === 'string' && item.source.trim() !== '' ? item.source : 'https://via.placeholder.com/300x200?text=No+Image' }}
        style={styles.media}
        resizeMode="cover"
        repeat
        muted
      />
    )}

    <View style={styles.overlay}>
      <Icon name="eye-outline" size={24} color={Color.Secondary} />
      <Text style={styles.title}>{ formatNumber(item.count)}</Text>
    </View>
  </TouchableOpacity>
</ScrollView>

  );
};

export default PostCard;
const styles = StyleSheet.create({
  card: {
    backgroundColor: '#1c1c2b',
    borderRadius: 12,
    margin: 5,
    padding: 0, // remove padding to prevent overlay misalignment
    width: CARD_WIDTH,
    overflow: 'hidden',
    position: 'relative',
  },
  media: {
    width: '100%',
    height: 150,
    borderRadius: 10,
  },
  overlay: {
    position: 'absolute',
    flexDirection :'row',
    alignItems :'center',
    gap : 8,
    bottom: 10,
    // left: 7,
    // backgroundColor: 'rgba(0,0,0,0.5)', // optional
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  title: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
});

//   card: {
//     backgroundColor: '#1c1c2b',
//     borderRadius: 12,
//     margin: 5,
//     padding: 8,
//     width: CARD_WIDTH,
//     overflow: 'hidden',
//   },
//   title: {
//     color: '#fff',
//     fontWeight: 'bold',
//     marginBottom: 8,
//     fontSize: 14,
//   },
//   media: {
//     width: '100%',
//     height: 150,
//     borderRadius: 10,
//   },
// });
