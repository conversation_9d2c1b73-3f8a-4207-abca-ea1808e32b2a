import React, { useState } from 'react';
import {  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';

import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Color } from '../constant/color';
import CustomToast from './CustomToast';


const ActionSheetModal = ({
  visible,
  onClose,
  onEdit,
  onReply,
  onCopy,
  onDelete,
  selectedMessage,
  onWallpaperPick,
  onClearChat,
}) => {
  const [loadingKey, setLoadingKey] = useState(null);
  const [toastData, setToastData] = useState(null);

  // Helper to handle async actions and show toast
  const handleAction = async (action, label) => {
    if (!action) return;
    setLoadingKey(label);
    try {
      const result = await action();
      setToastData({ type: 'success', message: result?.message || `${label} successful!` });
    } catch (err) {
      setToastData({ type: 'error', message: err?.message || `${label} failed!` });
    } finally {
      setLoadingKey(null);
      onClose && onClose();
    }
  };

  return (
    <Modal
      transparent
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <View style={styles.container}>
            {selectedMessage ? (
              // Message-specific actions
              <>
                <TouchableOpacity style={styles.option} onPress={() => handleAction(onReply, 'Reply')} disabled={!!loadingKey}>
                  <Icon name="reply" size={22} color={Color.white} />
                  <Text style={styles.optionText}>Reply</Text>
                  {loadingKey === 'Reply' && <ActivityIndicator size="small" color="#fff" style={{ marginLeft: 8 }} />}
                </TouchableOpacity>

                {selectedMessage.sender && selectedMessage.text && (
                  <TouchableOpacity style={styles.option} onPress={() => handleAction(onEdit, 'Edit')} disabled={!!loadingKey}>
                    <Icon name="pencil" size={22} color={Color.white} />
                    <Text style={styles.optionText}>Edit</Text>
                    {loadingKey === 'Edit' && <ActivityIndicator size="small" color="#fff" style={{ marginLeft: 8 }} />}
                  </TouchableOpacity>
                )}

                {selectedMessage.text && (
                  <TouchableOpacity style={styles.option} onPress={() => handleAction(onCopy, 'Copy')} disabled={!!loadingKey}>
                    <Icon name="content-copy" size={22} color={Color.white} />
                    <Text style={styles.optionText}>Copy</Text>
                    {loadingKey === 'Copy' && <ActivityIndicator size="small" color="#fff" style={{ marginLeft: 8 }} />}
                  </TouchableOpacity>
                )}

                <TouchableOpacity style={styles.option} onPress={() => handleAction(onDelete, 'Delete')} disabled={!!loadingKey}>
                  <Icon name="delete" size={22} color={Color.red} />
                  <Text style={[styles.optionText, { color: Color.red }]}>Delete</Text>
                  {loadingKey === 'Delete' && <ActivityIndicator size="small" color={Color.red} style={{ marginLeft: 8 }} />}
                </TouchableOpacity>
              </>
            ) : (
              // General chat actions
              <>
                {onWallpaperPick && (
                  <TouchableOpacity style={styles.option} onPress={() => handleAction(onWallpaperPick, 'Change Wallpaper')} disabled={!!loadingKey}>
                    <Icon name="image" size={22} color={Color.white} />
                    <Text style={styles.optionText}>Change Wallpaper</Text>
                    {loadingKey === 'Change Wallpaper' && <ActivityIndicator size="small" color="#fff" style={{ marginLeft: 8 }} />}
                  </TouchableOpacity>
                )}

                {onClearChat && (
                  <TouchableOpacity style={styles.option} onPress={() => handleAction(onClearChat, 'Clear Chat')} disabled={!!loadingKey}>
                    <Icon name="delete-sweep" size={22} color={Color.red} />
                    <Text style={[styles.optionText, { color: Color.red }]}>Clear Chat</Text>
                    {loadingKey === 'Clear Chat' && <ActivityIndicator size="small" color={Color.red} style={{ marginLeft: 8 }} />}
                  </TouchableOpacity>
                )}

                {onCopy && (
                  <TouchableOpacity style={styles.option} onPress={() => handleAction(onCopy, 'Copy All Messages')} disabled={!!loadingKey}>
                    <Icon name="content-copy" size={22} color={Color.white} />
                    <Text style={styles.optionText}>Copy All Messages</Text>
                    {loadingKey === 'Copy All Messages' && <ActivityIndicator size="small" color="#fff" style={{ marginLeft: 8 }} />}
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
          {toastData && (
            <CustomToast
              type={toastData.type}
              message={toastData.message}
              onHide={() => setToastData(null)}
            />
          )}
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000066',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 16,
    width: '80%',
    elevation: 5,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  optionText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 12,
  },
});

export default ActionSheetModal;
