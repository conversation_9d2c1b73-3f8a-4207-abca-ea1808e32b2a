import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { BlurView } from '@react-native-community/blur';
import OTPTextInput from 'react-native-otp-textinput';
import { fontFamilies } from '../../constant/Font';
import { Color } from '../../constant/color';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { verifyOTP } from '../../redux/action/AuthAction';

import CustomToast from '../../component/CustomToast';

const OTPVerification = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const { loading } = useSelector(state => state.auth || {});

  const otpRef = useRef();
  const [otp, setOtp] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => setToastData({ type, message });
  const hideToast = () => setToastData(null);

  // Get email from route params
  const { email, otp: receivedOtp } = route.params || {};

  const handleOTPChange = (otpValue) => {
    console.log('Entered OTP:', otpValue);
    setOtp(otpValue);
  };

  const handleVerifyOTP = async () => {
    setSubmitted(true);

    if (!otp || otp.length !== 6) {
      showToast('error', 'Please enter a valid 6-digit OTP');
      return;
    }

    if (!email) {
      showToast('error', 'Email not found. Please go back and try again.');
      return;
    }

    try {
      console.log('Attempting to verify OTP:', { email, otp });

      // Dispatch verify OTP action
      const result = await verifyOTP(email, otp)(dispatch);

      console.log('Verify OTP result:', result);

      if (result.success) {
        showToast('success', 'OTP verified successfully!');
        // Navigate to new password screen
        navigation.navigate('NewPassword', { email, otp });
      } else {
        showToast('error', result.message || 'Invalid OTP');
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      showToast('error', 'OTP verification failed. Please try again.');
    }
  };

  return (
    <>
      <LinearGradient
        colors={['#333399', '#ff00cc']}
        style={styles.background}
        blurRadius={Platform.OS === 'android' ? 10 : 0}
      >
        <View style={styles.circleTopLeft} />
        <View style={styles.circleBottomRight} />
        <View style={styles.overlay}>
          {Platform.OS === 'ios' && (
            <BlurView style={styles.glass} blurType="light" blurAmount={20} />
          )}
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.glass}
          >
            <Text style={styles.title}>OTP Verification</Text>
            <Text style={styles.subtitle}>Enter the 6-digit code sent to {email}</Text>
            {receivedOtp && (
              <Text style={styles.debugText}>Debug: OTP is {receivedOtp}</Text>
            )}
            <OTPTextInput
              ref={otpRef}
              inputCount={6}
              handleTextChange={handleOTPChange}
              tintColor="#fff"
              offTintColor="rgba(255,255,255,0.3)"
              containerStyle={{ marginBottom: 20 }}
              textInputStyle={styles.otpInputStyle}
            />
            <TouchableOpacity
              onPress={handleVerifyOTP}
              style={[styles.button, loading && styles.buttonDisabled]}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="#fff" />
                  <Text style={[styles.buttonText, { marginLeft: 10 }]}>Verifying...</Text>
                </View>
              ) : (
                <Text style={styles.buttonText}>Verify</Text>
              )}
            </TouchableOpacity>
            <Text style={styles.footerText}>
              Didn’t receive the code?{' '}
              <Text style={{ color: Color.Secondary, textDecorationLine: 'underline' }}>Resend</Text>
            </Text>
          </LinearGradient>
        </View>
      </LinearGradient>
      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

export default OTPVerification;

const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'center',
  },
  overlay: {
    margin: 20,
    overflow: 'hidden',
  },
  circleTopLeft: {
    position: 'absolute',
    top: -60,
    left: -60,
    width: 180,
    height: 180,
    borderRadius: 90,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  circleBottomRight: {
    position: 'absolute',
    bottom: -40,
    right: -40,
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  glass: {
    padding: 30,
    borderRadius: 20,
    borderColor: 'rgba(255,255,255,0.3)',
    borderWidth: 1,
    backgroundColor: Platform.OS === 'android' ? 'rgba(255,255,255,0.1)' : 'transparent',
  },
  title: {
    fontSize: 26,
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.extraBold,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 14,
    color: '#ccc',
    fontFamily: fontFamilies.POPPINS.medium,
    textAlign: 'center',
    marginBottom: 20,
  },
  debugText: {
    fontSize: 12,
    color: '#ffeb3b',
    fontFamily: fontFamilies.POPPINS.regular,
    textAlign: 'center',
    marginBottom: 10,
    backgroundColor: 'rgba(255,235,59,0.1)',
    padding: 5,
    borderRadius: 5,
  },
  otpInputStyle: {
    backgroundColor: 'rgba(255,255,255,0.15)',
    borderRadius: 10,
    color: '#fff',
    fontSize: 18,
    borderWidth: 0,
  },
  button: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 12,
    borderRadius: 10,
    marginTop: 10,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  buttonDisabled: {
    backgroundColor: 'rgba(255,255,255,0.08)',
    borderColor: 'rgba(255,255,255,0.15)',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 18,
    fontFamily: fontFamilies.POPPINS.bold,
    textAlign: 'center',
  },
  footerText: {
    color: '#ccc',
    textAlign: 'center',
    marginTop: 15,
    fontFamily: fontFamilies.POPPINS.regular,
  },
});
