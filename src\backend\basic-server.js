const express = require('express');

console.log('🚀 Starting Basic Server...');

const app = express();

// Manual CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }
  next();
});

// JSON parser middleware
app.use(express.json());

console.log('✅ Middleware configured');

// Test route
app.get('/', (req, res) => {
  console.log('📍 Root route accessed');
  res.json({ 
    message: 'Basic Server is working!',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Register API
app.post('/api/auth/register', (req, res) => {
  console.log('📝 Register API called:', req.body);
  
  const { firstName, lastName, email, password, phone, address } = req.body;
  
  if (!firstName || !lastName || !email || !password || !phone || !address) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required'
    });
  }

  // Mock successful registration
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    token: 'mock-token-' + Date.now(),
    user: {
      id: 'user-' + Date.now(),
      name: `${firstName} ${lastName}`,
      firstName,
      lastName,
      email,
      phone,
      address,
      isVerified: false,
      isProfileComplete: false
    }
  });
});

// Login API
app.post('/api/auth/login', (req, res) => {
  console.log('🔐 Login API called:', req.body);
  
  const { email, password } = req.body;
  
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Email and password are required'
    });
  }

  // Mock successful login
  res.json({
    success: true,
    message: 'Login successful',
    token: 'mock-token-' + Date.now(),
    user: {
      id: 'user-123',
      name: 'Test User',
      email: email,
      phone: '1234567890',
      address: '123 Test Street',
      isVerified: true,
      isProfileComplete: true
    }
  });
});

// Complete Profile API
app.post('/api/auth/complete-profile', (req, res) => {
  console.log('👤 Complete Profile API called:', req.body);
  
  res.json({
    success: true,
    message: 'Profile completed successfully',
    user: {
      id: 'user-123',
      name: 'Test User',
      bio: req.body.bio || 'Test bio',
      gender: req.body.gender || 'male',
      isProfileComplete: true
    }
  });
});

// Forgot Password API
app.post('/api/auth/forgot-password', (req, res) => {
  console.log('🔑 Forgot Password API called:', req.body);
  
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  // Mock OTP generation
  const otp = '123456';
  
  res.json({
    success: true,
    message: 'OTP sent to your email',
    otp: otp // In real app, don't send OTP in response
  });
});

// Verify OTP API
app.post('/api/auth/verify-otp', (req, res) => {
  console.log('🔢 Verify OTP API called:', req.body);
  
  const { email, otp } = req.body;
  
  if (!email || !otp) {
    return res.status(400).json({
      success: false,
      message: 'Email and OTP are required'
    });
  }

  // Mock OTP verification (accept 123456)
  if (otp === '123456') {
    res.json({
      success: true,
      message: 'OTP verified successfully'
    });
  } else {
    res.status(400).json({
      success: false,
      message: 'Invalid OTP'
    });
  }
});

// Reset Password API
app.post('/api/auth/reset-password', (req, res) => {
  console.log('🔐 Reset Password API called:', req.body);
  
  const { email, otp, newPassword } = req.body;
  
  if (!email || !otp || !newPassword) {
    return res.status(400).json({
      success: false,
      message: 'Email, OTP and new password are required'
    });
  }

  // Mock password reset
  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

const PORT = 3001;

app.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Server failed to start:', err);
    return;
  }
  console.log(`🚀 Basic Server running on port ${PORT}`);
  console.log(`📍 Local: http://localhost:${PORT}`);
  console.log(`📍 Network: http://***************:${PORT}`);
  console.log('✅ Server started successfully!');
});

console.log('📝 Server setup complete');
