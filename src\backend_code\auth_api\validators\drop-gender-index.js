// drop-gender-index.js
const mongoose = require('mongoose');

const MONGO_URI = 'mongodb+srv://app:<EMAIL>/test?retryWrites=true&w=majority&appName=app';

mongoose.connect(MONGO_URI)
  .then(async () => {
    console.log('Connected to MongoDB');
    try {
      const result = await mongoose.connection.db.collection('users').dropIndex('gender_1');
      console.log('gender_1 index dropped:', result);
    } catch (err) {
      console.error('Drop error:', err.message);
    } finally {
      mongoose.disconnect();
    }
  })
  .catch(err => {
    console.error('MongoDB connection error:', err.message);
  });
