import { StyleSheet, Text, TouchableOpacity, View, Animated, Dimensions } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import LinearGradient from 'react-native-linear-gradient'
import Icon from 'react-native-vector-icons/Ionicons'
import PostComponent from './PostComponent'
import PrivateComponent from './PrivateComponent'
import SavePostComponent from './SavePostComponent'
import LikePostComponent from './LikePostComponent'

const { width } = Dimensions.get('window');
const TAB_WIDTH = width / 4;

const ProfileTapComponent = () => {
  const [tap, setTap] = useState(1);
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.spring(animatedValue, {
      toValue: (tap - 1) * TAB_WIDTH + TAB_WIDTH / 2 - 20,
      useNativeDriver: false,
    }).start();
  }, [tap]);

  const icons = ['grid-outline', 'lock-closed-outline', 'bookmark-outline', 'heart-outline'];
  const labels = ['Post', 'Private', 'Saves', 'Likes'];
  const gradients = [
    ['#007cf0', '#00dfd8'],
    ['#8e2de2', '#4a00e0'],
    ['#ff6a00', '#ee0979'],
    ['#ff6a00', '#ee0979'],
  ];

  return (
    <>
      <View style={styles.featuresContainer}>
        {[1, 2, 3, 4].map((item, index) => {
          const isActive = tap === item;

          return (
            <TouchableOpacity
              key={index}
              style={styles.tabWrapper}
              onPress={() => setTap(item)}
              activeOpacity={0.8}
            >
              <View style={styles.featureBox}>
                <LinearGradient
                  colors={gradients[index]}
                  style={styles.featureGradient}
                >
                  <Icon
                    name={icons[index]}
                    size={20}
                    color="#fff"
                  />
                </LinearGradient>

                {/* Show label only for active tab */}
                {isActive && (
                  <Text style={styles.featureLabel}>
                    {labels[index]}
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          );
        })}

        {/* Animated underline */}
        <Animated.View
          style={[
            styles.animatedLine,
            {
              transform: [{ translateX: animatedValue }],
            },
          ]}
        />
      </View>

      {/* Render Tab Component */}
      {tap === 1 && <PostComponent />}
      {tap === 2 && <PrivateComponent />}
      {tap === 3 && <SavePostComponent />}
      {tap === 4 && <LikePostComponent />}
    </>
  );
};

export default ProfileTapComponent;

const styles = StyleSheet.create({
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 30,
    width: '100%',
    backgroundColor: '#0D0D1F',
    paddingBottom: 8,
    position: 'relative',
  },
  tabWrapper: {
    alignItems: 'center',
    width: TAB_WIDTH,
  },
  featureBox: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
    height: 80,
    width: 60,
    backgroundColor: '#BBBEC1',
  },
  featureGradient: {
    width: 30,
    height: 30,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  featureLabel: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
  },
  animatedLine: {
    position: 'absolute',
    bottom: 0,
    height: 3,
    width: 40,
    borderRadius: 2,
    backgroundColor: 'red',
  },
});
