import AsyncStorage from '@react-native-async-storage/async-storage';

// API Base URL
const BASE_URL = 'http://192.168.100.150:3002/api';

// Action Types
export const SET_LOADING = 'SET_CHAT_LOADING';
export const SET_ERROR = 'SET_CHAT_ERROR';
export const CLEAR_ERROR = 'CLEAR_CHAT_ERROR';
export const SET_CHAT_LIST = 'SET_CHAT_LIST';
export const SET_MESSAGES = 'SET_MESSAGES';
export const ADD_MESSAGE = 'ADD_MESSAGE';
export const SET_NOTIFICATIONS = 'SET_NOTIFICATIONS';
export const ADD_NOTIFICATION = 'ADD_NOTIFICATION';

// Action Creators
export const setLoading = (loading) => ({
  type: SET_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: SET_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

export const setChatList = (chats) => ({
  type: SET_CHAT_LIST,
  payload: chats
});

export const setMessages = (messages) => ({
  type: SET_MESSAGES,
  payload: messages
});

export const addMessage = (message) => ({
  type: ADD_MESSAGE,
  payload: message
});

export const setNotifications = (notifications) => ({
  type: SET_NOTIFICATIONS,
  payload: notifications
});

export const addNotification = (notification) => ({
  type: ADD_NOTIFICATION,
  payload: notification
});

// Get Chat List (Inbox)
export const getChatList = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/chat`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get chat list API response:', result);

      if (result.success) {
        dispatch(setChatList(result.chats));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, chats: result.chats };
    } catch (error) {
      console.error('Get chat list error:', error);
      dispatch(setError(error.message || 'Failed to get chat list'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Get Messages with User
export const getMessages = (userId) => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/chat/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get messages API response:', result);

      if (result.success) {
        dispatch(setMessages(result.messages));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, messages: result.messages, user: result.user };
    } catch (error) {
      console.error('Get messages error:', error);
      dispatch(setError(error.message || 'Failed to get messages'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Send Message
export const sendMessage = (userId, text) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/chat/${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ text })
      });

      const result = await response.json();
      console.log('Send message API response:', result);

      if (result.success) {
        dispatch(addMessage(result.data));
      }

      return { success: result.success, data: result, message: result.data };
    } catch (error) {
      console.error('Send message error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Get Notifications
export const getNotifications = () => {
  return async (dispatch) => {
    try {
      dispatch(setLoading(true));
      dispatch(clearError());

      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/notifications`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Get notifications API response:', result);

      if (result.success) {
        dispatch(setNotifications(result.notifications));
      }

      dispatch(setLoading(false));
      return { success: result.success, data: result, notifications: result.notifications };
    } catch (error) {
      console.error('Get notifications error:', error);
      dispatch(setError(error.message || 'Failed to get notifications'));
      dispatch(setLoading(false));
      return { success: false, error: error.message };
    }
  };
};

// Mark Notification as Read
export const markNotificationRead = (notificationId) => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Mark notification read API response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Mark notification read error:', error);
      return { success: false, error: error.message };
    }
  };
};

// Mark All Notifications as Read
export const markAllNotificationsRead = () => {
  return async (dispatch) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) {
        throw new Error('No token found');
      }

      const response = await fetch(`${BASE_URL}/notifications/read-all`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();
      console.log('Mark all notifications read API response:', result);

      return { success: result.success, data: result };
    } catch (error) {
      console.error('Mark all notifications read error:', error);
      return { success: false, error: error.message };
    }
  };
};
