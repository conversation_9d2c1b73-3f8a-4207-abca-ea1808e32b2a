import {
  Dimensions,
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ActivityIndicator,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { Color } from '../../constant/color';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation, useRoute } from '@react-navigation/native';
import PostCard from '../../component/PostCard';
import { fontFamilies } from '../../constant/Font';
import SecarchCard from '../../component/SecarchCard';
import { useDispatch, useSelector } from 'react-redux';
import { searchUsers } from '../../redux/action/UserAction';
import { getPosts } from '../../redux/action/PostAction';
import CustomToast from '../../component/CustomToast';

const PostData = [
  { id: 1, data: "7:05:2025", tittle: "Post One",
      description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', 
      mediaType: 'image', source: require("../../assets/image/music.jpg") },
  { id: 2, data: "7:05:2025", tittle: "Cool Video", description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', mediaType: 'video', source: require("../../assets/video/video_2.mp4") },
  { id: 3, data: "7:05:2025", tittle: "Nice Tune", description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', mediaType: 'image', source: require("../../assets/image/music.jpg") },
  { id: 4, data: "7:05:2025", tittle: "Dance Clip", description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', mediaType: 'video', source: require("../../assets/video/video_2.mp4") },
  { id: 5, data: "7:05:2025", tittle: "My Melody", description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', mediaType: 'image', source: require("../../assets/image/music.jpg") },
  { id: 6, data: "7:05:2025", tittle: "Beats Track", description: 'hello #hvjnfjd cvd #dvnrjf sdjbn djsdj', mediaType: 'image', source: require("../../assets/image/music.jpg") },
]


const SearchScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { searchResults, loading: userLoading } = useSelector(state => state.users || {});
  const { posts, loading: postLoading } = useSelector(state => state.posts || {});

  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('users'); // 'users' or 'posts'
  const [filteredData, setFilteredData] = useState([]);
  const [toastData, setToastData] = useState(null);
  const h = Dimensions.get('screen').height;

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  useEffect(() => {
    if (route.params?.tag) {
      setSearchText(route.params?.tag);
      handleSearch(route.params?.tag);
    }
  }, [route.params?.tag]);

  const handleSearch = async (text = '') => {
    setSearchText(text);

    if (!text.trim()) {
      setFilteredData([]);
      return;
    }

    try {
      if (searchType === 'users') {
        const result = await searchUsers(text)(dispatch);
        if (result.success) {
          setFilteredData(result.users);
        } else {
          showToast('error', result.error || 'Failed to search users');
        }
      } else {
        // Search in posts (filter locally for now)
        const searchQuery = text.toLowerCase();
        const result = posts?.filter(item =>
          item?.description?.toLowerCase()?.includes(searchQuery) ||
          item?.user?.name?.toLowerCase()?.includes(searchQuery)
        ) || [];
        setFilteredData(result);
      }
    } catch (error) {
      console.error('Search error:', error);
      showToast('error', 'Search failed');
    }
  };


  const renderUserItem = ({ item }) => (
    <TouchableOpacity
      style={styles.userItem}
      onPress={() => navigation.navigate('ViewInboxProfile', { userId: item.id })}
    >
      <Image
        source={item.profileImage ? { uri: `http://**************:3001/uploads/profiles/${item.profileImage}` } : require('../../assets/image/music.jpg')}
        style={styles.userAvatar}
      />
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.name}</Text>
        <Text style={styles.userBio}>{item.bio || 'No bio available'}</Text>
        <Text style={styles.userStats}>{item.followersCount} followers</Text>
      </View>
      {item.isVerified && (
        <Icon name="checkmark-circle" size={20} color="#1DA1F2" />
      )}
    </TouchableOpacity>
  );

  const renderPostItem = ({ item }) => (
    <SecarchCard item={item} />
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inputContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon style={styles.backArrow} name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>

        <View style={{ flexDirection: 'row', paddingHorizontal: 20, flex: 1 }}>
          <TextInput
            style={styles.input}
            placeholder='Search users, posts...'
            placeholderTextColor={Color.Primary}
            value={searchText}
            onChangeText={handleSearch}
            autoFocus={true}
          />
          <TouchableOpacity style={styles.searchButton} onPress={() => handleSearch(searchText)}>
            <Icon name="search" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Type Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, searchType === 'users' && styles.activeTab]}
          onPress={() => {
            setSearchType('users');
            if (searchText) handleSearch(searchText);
          }}
        >
          <Text style={[styles.tabText, searchType === 'users' && styles.activeTabText]}>Users</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, searchType === 'posts' && styles.activeTab]}
          onPress={() => {
            setSearchType('posts');
            if (searchText) handleSearch(searchText);
          }}
        >
          <Text style={[styles.tabText, searchType === 'posts' && styles.activeTabText]}>Posts</Text>
        </TouchableOpacity>
      </View>

      {/* Loading State */}
      {(userLoading || postLoading) ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : filteredData.length > 0 ? (
        <FlatList
          data={filteredData}
          keyExtractor={(item) => item.id?.toString() || Math.random().toString()}
          renderItem={searchType === 'users' ? renderUserItem : renderPostItem}
          numColumns={searchType === 'posts' ? 2 : 1}
          contentContainerStyle={styles.list}
          showsVerticalScrollIndicator={false}
        />
      ) : searchText ? (
        <View style={styles.emptyContainer}>
          <Image style={styles.emptyImage} source={require('../../assets/image/notFondImage.png')} />
          <Text style={styles.emptyText}>No {searchType} found for "{searchText}"</Text>
          <Text style={styles.emptySubText}>Try searching with different keywords</Text>
        </View>
      ) : (
        <View style={styles.emptyContainer}>
          <Icon name="search" size={60} color="#666" />
          <Text style={styles.emptyText}>Search for users and posts</Text>
          <Text style={styles.emptySubText}>Discover new content and connect with people</Text>
        </View>
      )}

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default SearchScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Color.Main,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  input: {
    flex: 1,
    backgroundColor: Color.inputBg,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    padding: 10,
    color: '#fff',
  },
  searchButton: {
    backgroundColor: 'green',
    height: 38,
    width: 38,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  list: {
    padding: 10,
  },
  backArrow: {},
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Color.inputBg,
    marginHorizontal: 15,
    borderRadius: 10,
    marginBottom: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: Color.Primary,
  },
  tabText: {
    color: '#ccc',
    fontSize: 16,
    fontWeight: '600',
  },
  activeTabText: {
    color: '#fff',
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginHorizontal: 15,
    marginVertical: 5,
    backgroundColor: Color.inputBg,
    borderRadius: 10,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  userBio: {
    color: '#ccc',
    fontSize: 14,
    marginBottom: 2,
  },
  userStats: {
    color: '#999',
    fontSize: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyImage: {
    width: 60,
    height: 60,
    marginBottom: 20,
  },
  emptyText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  emptySubText: {
    color: '#ccc',
    fontSize: 14,
    textAlign: 'center',
  },
});
