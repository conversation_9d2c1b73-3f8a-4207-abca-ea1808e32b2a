import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  SafeAreaView,
  ActivityIndicator,
  StatusBar,
  Dimensions,
  Text,
  RefreshControl,
} from 'react-native';
import ForyouVideoComponent from '../../component/ForyouVideoComponent';
import DashBoardTabs from './DashBoardTabs';
import { useDispatch, useSelector } from 'react-redux';
import { getPosts } from '../../redux/action/PostAction';
import CustomToast from '../../component/CustomToast';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Fallback data for when API is loading or fails
const fallbackData = [
  {
    id: 'fallback-1',
    tittle: 'Jhon',
    description: 'Amazing video content! #video #amazing #viral',
    media: require('../../assets/video/video_2.mp4'),
    mediaType: 'video',
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
    likesCount: 1250,
    commentsCount: 89,
    isLiked: false,
    user: { name: 'Jhon', profileImage: null }
  },
  {
    id: 'fallback-2',
    tittle: 'Sarah',
    description: 'Beautiful sunset photo #photography #sunset #nature #beautiful #landscape #golden #hour #peaceful #serene #amazing',
    media: require('../../assets/image/music.jpg'),
    mediaType: 'image',
    musicImage: require('../../assets/image/music.jpg'),
    ProfileImage: require('../../assets/image/music.jpg'),
    likesCount: 890,
    commentsCount: 67,
    isLiked: false,
    user: { name: 'Sarah', profileImage: null }
  }
];

const Dashboard = () => {
  const dispatch = useDispatch();
  const { posts, loading } = useSelector(state => state.posts || {});

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [toastData, setToastData] = useState(null);

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const viewabilityConfig = useRef({
    viewAreaCoveragePercentThreshold: 50,
  }).current;

  const onViewRef = useRef(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      setSelectedIndex(viewableItems[0].index);
    }
  });

  // Load posts on component mount
  useEffect(() => {
    loadPosts();
  }, []);

  const loadPosts = async () => {
    try {
      const result = await getPosts()(dispatch);
      if (!result.success) {
        showToast('error', result.error || 'Failed to load posts');
      }
    } catch (error) {
      console.error('Load posts error:', error);
      showToast('error', 'Failed to load posts');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPosts();
    setRefreshing(false);
  };

  // Use API posts or fallback data
  const displayData = posts && posts.length > 0 ? posts : fallbackData;

  const renderItem = ({ item, index }) => (
    <ForyouVideoComponent
      index={index}
      items={item}
      musicImage={item.musicImage || require('../../assets/image/music.jpg')}
      item={item.videoUrl || item.media || require('../../assets/video/video_2.mp4')}
      mediaType={item.video ? 'video' : item.image ? 'image' : item.mediaType || 'video'}
      ProfileImage={item.user?.profileImage || item.ProfileImage || require('../../assets/image/music.jpg')}
      isPlay={index === selectedIndex}
      postData={item}
      onDeleteVideo={(videoId) => {
        console.log('Delete video:', videoId);
        // Handle video deletion logic here
      }}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="black" barStyle="light-content"  />
      <DashBoardTabs />
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#fff" />
        </View>
      ) : (
        <FlatList
          data={displayData}
          renderItem={renderItem}
          keyExtractor={(item, index) => item.id?.toString() || index.toString()}
          showsVerticalScrollIndicator={false}
          pagingEnabled
          onViewableItemsChanged={onViewRef.current}
          viewabilityConfig={viewabilityConfig}
          style={styles.flatList}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#fff"
            />
          }
          getItemLayout={(_, index) => ({
            length: screenHeight,
            offset: screenHeight * index,
            index,
          })}
          snapToInterval={screenHeight}
          snapToAlignment="start"
          decelerationRate="fast"
        />
      )}

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </SafeAreaView>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
  },
  flatList: {
    flex: 1,
    height: screenHeight,
  },
});
