import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser } from '../redux/action/AuthAction';
import { fontFamilies } from '../constant/Font';
import { Color } from '../constant/color';
import CustomToast from './CustomToast';
import { useNavigation } from '@react-navigation/native';

const LogoutButton = ({ style, textStyle }) => {
  const dispatch = useDispatch();
  const { loading } = useSelector(state => state.auth || {});
  const [toastData, setToastData] = useState(null);
  const navigation = useNavigation();

  const showToast = (type, message) => {
    setToastData({ type, message });
  };

  const hideToast = () => {
    setToastData(null);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('Attempting to logout...');
              
              const result = await logoutUser()(dispatch);
              
              console.log('Logout result:', result);

              if (result.success) {
                showToast('success', 'Logged out successfully!');
                // Navigation will happen automatically through AppNavigator
                // when Redux state updates and isAuth becomes false
              } else {
                showToast('error', result.error || 'Logout failed');
              }
            } catch (error) {
              console.error('Logout error:', error);
              showToast('error', 'Logout failed. Please try again.');
            }
          },
        },
      ]
    );
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.logoutButton, style]}
        onPress={handleLogout}
        disabled={loading}
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#fff" />
            <Text style={[styles.logoutText, textStyle, { marginLeft: 10 }]}>
              Logging out...
            </Text>
          </View>
        ) : (
          <Text style={[styles.logoutText, textStyle]}>Logout</Text>
        )}
      </TouchableOpacity>

      {toastData && (
        <CustomToast
          type={toastData.type}
          message={toastData.message}
          onHide={hideToast}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  logoutButton: {
    backgroundColor: Color.red || '#ff4444',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS?.medium || 'System',
    fontWeight: '600',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default LogoutButton;
