const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({ 
    message: 'Test server is working!',
    timestamp: new Date().toISOString()
  });
});

// Register route for testing
app.post('/api/auth/register', (req, res) => {
  console.log('Register request received:', req.body);
  res.json({
    success: true,
    message: 'Test register endpoint working',
    data: req.body
  });
});

// Login route for testing
app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.json({
    success: true,
    message: 'Test login endpoint working',
    token: 'test-token-123',
    user: {
      id: '1',
      name: 'Test User',
      email: req.body.email
    }
  });
});

const PORT = 3001;

app.listen(PORT, () => {
  console.log(`🚀 Test Server running on port ${PORT}`);
  console.log(`📍 Server URL: http://localhost:${PORT}`);
  console.log(`📋 Register URL: http://localhost:${PORT}/api/auth/register`);
  console.log(`📋 Login URL: http://localhost:${PORT}/api/auth/login`);
  console.log('✅ Server started successfully!');
});
