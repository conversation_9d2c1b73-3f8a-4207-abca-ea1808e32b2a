import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  FlatList,
  TextInput,
} from 'react-native';
import React, { useState } from 'react';
import Icon from 'react-native-vector-icons/Ionicons';
import { Color } from '../../constant/color';
import { fontFamilies } from '../../constant/Font';

const reasons = [
  'Spam or scam',
  'Harassment or bullying',
  'Inappropriate content',
  'Fake profile',
  'Other',
];

const ReportUser = ({ navigation }) => {
  const [selectedReason, setSelectedReason] = useState(null);
  const [additionalInfo, setAdditionalInfo] = useState('');

  const handleSubmit = () => {
    if (selectedReason) {
      // Submit logic here
      alert('User reported. Thank you!');
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backBtn}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Report User</Text>
      </View>

      {/* Subtext */}
      <Text style={styles.subtext}>Why are you reporting this user?</Text>

      {/* Report Reason Options */}
      <FlatList
        data={reasons}
        keyExtractor={(item) => item}
        style={styles.reasonList}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.reasonItem,
              selectedReason === item && styles.reasonItemSelected,
            ]}
            onPress={() => setSelectedReason(item)}
          >
            <Text
              style={[
                styles.reasonText,
                selectedReason === item && styles.reasonTextSelected,
              ]}
            >
              {item}
            </Text>
            {selectedReason === item && <Icon name="checkmark" size={20} color="#fff" />}
          </TouchableOpacity>
        )}
      />

      {/* Optional Message Box */}
      <Text style={styles.optionalLabel}>Additional details (optional)</Text>
      <TextInput
        placeholder="Write here..."
        placeholderTextColor="#aaa"
        multiline
        value={additionalInfo}
        onChangeText={setAdditionalInfo}
        style={styles.textArea}
      />

      {/* Submit Button */}
      <TouchableOpacity
        style={[
          styles.submitBtn,
          !selectedReason && { backgroundColor: '#555' },
        ]}
        disabled={!selectedReason}
        onPress={handleSubmit}
      >
        <Text style={styles.submitText}>Submit Report</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default ReportUser;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0D0D1F',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backBtn: {
    paddingRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fontFamilies.POPPINS.medium,
    color: '#fff',
  },
  subtext: {
    color: '#ccc',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.regular,
    marginBottom: 15,
  },
  reasonList: {
    marginBottom: 20,
  },
  reasonItem: {
    backgroundColor: '#1A1A2E',
    borderRadius: 10,
    paddingVertical: 14,
    paddingHorizontal: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reasonItemSelected: {
    backgroundColor: Color.purple,
  },
  reasonText: {
    color: '#ccc',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.regular,
  },
  reasonTextSelected: {
    color: '#fff',
    fontFamily: fontFamilies.POPPINS.medium,
  },
  optionalLabel: {
    color: '#aaa',
    fontSize: 14,
    marginBottom: 5,
    fontFamily: fontFamilies.POPPINS.medium,
  },
  textArea: {
    backgroundColor: '#1A1A2E',
    borderRadius: 10,
    padding: 12,
    color: '#fff',
    height: 100,
    textAlignVertical: 'top',
    fontFamily: fontFamilies.POPPINS.regular,
    marginBottom: 25,
  },
  submitBtn: {
    backgroundColor: Color.pink,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
  },
  submitText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: fontFamilies.POPPINS.medium,
  },
});
