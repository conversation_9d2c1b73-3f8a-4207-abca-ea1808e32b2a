import React, { useState } from 'react';
import {
  Modal, Pressable, Text, TextInput, View,
  StyleSheet, FlatList, Image, TouchableOpacity
} from 'react-native';
import { Color } from '../constant/color';
import { fontFamilies } from '../constant/Font';

const MentionModal = ({ visible, onClose, onSelectMention }) => {
  const [search, setSearch] = useState('');

  const mockMentionData = [
    { id: '1', name: '<PERSON><PERSON>', profile: 'https://i.pravatar.cc/150?img=1' },
    { id: '2', name: '<PERSON>', profile: 'https://i.pravatar.cc/150?img=2' },
    { id: '3', name: '<PERSON>', profile: 'https://i.pravatar.cc/150?img=3' },
    { id: '4', name: '<PERSON>', profile: 'https://i.pravatar.cc/150?img=4' },
  ];

  const filteredMentions = mockMentionData.filter(user =>
    user.name.toLowerCase().includes(search.toLowerCase())
  );

  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.mentionItem}
      onPress={() => {
        onSelectMention(`@${item.name} `);
        onClose();
      }}
    >
      <Image source={{ uri: item.profile }} style={styles.avatar} />
      <View>
        <Text style={styles.name}>{item.name}</Text>
        <Text style={styles.id}>@{item.id}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal transparent animationType="slide" visible={visible} onRequestClose={onClose}>
      <Pressable style={styles.modalOverlay} onPress={onClose}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Mention</Text>
          <TextInput
            placeholder="Search Mention"
            placeholderTextColor={Color.gray}
            style={styles.mentionInput}
            value={search}
            onChangeText={setSearch}
          />

          <FlatList
            data={filteredMentions}
            keyExtractor={item => item.id}
            renderItem={renderItem}
            style={{ marginTop: 16 }}
            keyboardShouldPersistTaps="handled"
          />
        </View>
      </Pressable>
    </Modal>
  );
};

export default MentionModal;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  modalContent: {
    backgroundColor: '#fff',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 17,
    fontFamily: fontFamilies.POPPINS.extraBold,
  },
  mentionInput: {
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    padding: 10,
    marginTop: 10,
  },
  mentionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#eee',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  name: {
    fontFamily: fontFamilies.POPPINS.medium,
    fontSize: 15,
    color: '#000',
  },
  id: {
    fontSize: 13,
    color: 'gray',
  },
});
