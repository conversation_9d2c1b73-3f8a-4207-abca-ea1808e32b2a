import {
  SET_LOADING,
  SET_ERROR,
  CLEAR_ERROR,
  SET_POSTS,
  ADD_POST,
  UPDATE_POST,
  SET_COMMENTS,
  ADD_COMMENT
} from '../action/PostAction';

const initialState = {
  posts: [],
  comments: [],
  loading: false,
  error: null
};

const PostReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    case SET_POSTS:
      return {
        ...state,
        posts: action.payload,
        loading: false,
        error: null
      };

    case ADD_POST:
      return {
        ...state,
        posts: [action.payload, ...state.posts],
        loading: false,
        error: null
      };

    case UPDATE_POST:
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.id ? { ...post, ...action.payload } : post
        ),
        loading: false,
        error: null
      };

    case SET_COMMENTS:
      return {
        ...state,
        comments: action.payload,
        loading: false,
        error: null
      };

    case ADD_COMMENT:
      return {
        ...state,
        comments: [action.payload, ...state.comments],
        loading: false,
        error: null
      };

    default:
      return state;
  }
};

export default PostReducer;
