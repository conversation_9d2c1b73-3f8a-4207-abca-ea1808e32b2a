import React from 'react';
import { View, StyleSheet } from 'react-native';
import EmojiSelector from 'react-native-emoji-selector';

const EmojiPicker = ({ onEmojiSelected }) => {
  return (
    <View style={styles.container}>
      <EmojiSelector
        onEmojiSelected={onEmojiSelected}
        showSearchBar={false}
        showTabs={true}
        category="all"
        columns={8}
        emojiSize={28} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 250,
  },
});

export default EmojiPicker;
